<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA Order Converter - Jing Ge订单处理</title>
    <!-- API配置文件 - 统一管理API密钥 -->
    <script src="api-config.js"></script>
    <style>
        /* 基础样式 */
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --secondary-color: #6b7280;
            --secondary-hover: #4b5563;
            --success-color: #10b981;
            --success-hover: #059669;
            --danger-color: #ef4444;
            --danger-hover: #dc2626;
            --warning-color: #f59e0b;
            --warning-hover: #d97706;
            --border-color: #e5e7eb;
            --bg-color: #f9fafb;
            --text-color: #111827;
            --text-secondary: #6b7280;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --radius-sm: 0.25rem;
            --radius: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --transition: all 0.2s ease-in-out;
        }
        
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮 */
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            line-height: 1.5;
            font-size: 16px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            width: 100%;
            box-sizing: border-box;
        }
        
        header {
            background-color: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            box-shadow: var(--shadow-sm);
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-container img {
            height: 2rem;
            margin-right: 0.75rem;
            transition: var(--transition);
        }
        
        h1 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: var(--primary-color);
            transition: var(--transition);
        }
        
        main {
            flex: 1;
            padding: 1.5rem 0;
        }
        
        .card {
            background-color: white;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }
        
        .card:hover {
            box-shadow: var(--shadow);
        }
        
        .card-header {
            padding: 1.25rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(59, 130, 246, 0.03);
        }
        
        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            color: var(--primary-color);
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
            position: relative;
        }
        
        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
            transition: var(--transition);
        }
        
        .form-input, textarea, select {
            width: 100%;
            padding: 0.625rem 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background-color: #fff;
            color: var(--text-color);
            font-size: 1rem;
            line-height: 1.5;
            transition: var(--transition);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .form-input:hover, textarea:hover, select:hover {
            border-color: var(--primary-color);
        }
        
        .form-input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
        }
        
        .form-input[readonly], .form-input:disabled, textarea[readonly], select:disabled {
            background-color: var(--bg-color);
            cursor: not-allowed;
            opacity: 0.75;
            box-shadow: none;
        }
        
        textarea {
            min-height: 150px;
            resize: vertical;
            line-height: 1.6;
        }
        
        /* 移动端文本区域样式 */
        @media (max-width: 767px) {
            textarea {
                min-height: 120px;
            }
        }
        
        .btn {
            display: inline-flex;
            font-weight: 500;
            justify-content: center;
            align-items: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.625rem 1.25rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: var(--radius);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }
        
        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        
        .btn:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            20% {
                transform: scale(25, 25);
                opacity: 0.3;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }
        
        .btn-primary {
            color: #fff;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }
        
        .btn-primary:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-gray {
            color: #fff;
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .btn-gray:hover {
            background-color: var(--secondary-hover);
            border-color: var(--secondary-hover);
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }
        
        .btn-gray:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-green {
            color: #fff;
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .btn-green:hover {
            background-color: var(--success-hover);
            border-color: var(--success-hover);
            box-shadow: var(--shadow);
            transform: translateY(-1px);
        }
        
        .btn-green:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .button-group {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        /* 移动端按钮组样式 */
        @media (max-width: 767px) {
            .button-group {
                flex-direction: column;
            }
            
            .button-group .btn {
                width: 100%;
                margin-bottom: 0.5rem;
                min-height: 44px; /* 确保触摸目标足够大 */
            }
        }
        
        .flex-1 {
            flex: 1;
        }
        
        .hidden {
            display: none;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        @media (min-width: 1024px) {
            .grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        @media (min-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        /* 移动端适配 */
        @media (max-width: 767px) {
            .container {
                padding: 0 0.5rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .header-content a {
                margin-top: 0.5rem;
            }
            
            h1 {
                font-size: 1rem;
                margin-top: 0.5rem;
            }
            
            .logo-container img {
                height: 1.5rem;
            }
        }
        
        .col-span-2 {
            grid-column: span 1;
        }
        
        @media (min-width: 768px) {
            .col-span-2 {
                grid-column: span 2;
            }
        }
        
        footer {
            margin-top: auto;
            padding: 1rem 0;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            /* 兼容Edge浏览器 */
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        footer .container {
            /* 兼容旧版Edge浏览器，使用替代方案实现文本居中 */
            display: flex;
            justify-content: center;
        }
        
        .notification {
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            background-color: var(--success-color);
            color: white;
            padding: 0.75rem 1.25rem;
            border-radius: var(--radius);
            box-shadow: var(--shadow-md);
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            z-index: 50;
            max-width: 350px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        
        .notification::before {
            content: '✔';
            margin-right: 0.5rem;
            font-weight: bold;
        }
        
        /* 移动端通知样式 */
        @media (max-width: 767px) {
            .notification {
                bottom: 1rem;
                right: 1rem;
                left: 1rem;
                font-size: 0.875rem;
                padding: 0.75rem 1rem;
                max-width: none;
                border-radius: var(--radius-sm);
            }
        }
        
        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .order-item {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 1.25rem;
            transition: var(--transition);
            background-color: white;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }
        
        .order-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: var(--primary-color);
            opacity: 0;
            transition: var(--transition);
        }
        
        .order-item:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }
        
        .order-item:hover::after {
            opacity: 1;
        }
        
        /* 多订单样式 */
        .order-card {
            background: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            padding: 1.25rem;
            transition: var(--transition);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }
        
        .order-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background-color: var(--primary-color);
            opacity: 0;
            transition: var(--transition);
        }
        
        .order-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }
        
        .order-card:hover::after {
            opacity: 1;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .order-content p {
            margin: 0.3rem 0;
            font-size: 0.9rem;
        }
        
        /* 移动端多订单样式 */
        @media (max-width: 767px) {
            #orders-list {
                grid-template-columns: 1fr !important;
                gap: 0.75rem !important;
                padding: 0.75rem !important;
            }
            
            .order-item {
                padding: 0.75rem;
            }
            
            .order-item-mobile .order-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .order-item-mobile .order-header button {
                margin-top: 0.5rem;
                width: 100%;
            }
            
            .order-item-mobile .order-content {
                display: flex;
                flex-direction: column;
            }
            
            /* 表单元素在移动端的样式 */
            .form-input, textarea, select {
                font-size: 16px; /* 防止iOS自动缩放 */
                padding: 0.625rem;
            }
            
            /* 移动端卡片样式优化 */
            .card {
                border-radius: 0.375rem;
                margin-bottom: 1rem;
            }
            
            .card-header {
                padding: 0.875rem 1rem;
            }
            
            .card-title {
                font-size: 1rem;
            }
            
            /* 移动端按钮优化 */
            .btn {
                min-height: 44px; /* 确保触摸目标足够大 */
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        
        /* 等待动画样式 */
        #api-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.85);
            -webkit-backdrop-filter: blur(3px);
            backdrop-filter: blur(3px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        
        #api-loader.show {
            opacity: 1;
        }
        
        #api-loader .spinner-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: white;
            padding: 1.5rem;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-md);
        }
        
        #api-loader .spinner {
            border: 3px solid rgba(59, 130, 246, 0.2);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        
        #api-loader .spinner-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        /* 添加深色模式支持 */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #111827;
                --text-color: #f9fafb;
                --text-secondary: #9ca3af;
                --border-color: #374151;
            }
            
            body {
                background-color: var(--bg-color);
                color: var(--text-color);
            }
            
            header, footer, .card, .order-item, .order-card {
                background-color: #1f2937;
                border-color: var(--border-color);
            }
            
            .form-input, textarea, select {
                background-color: #111827;
                color: var(--text-color);
                border-color: var(--border-color);
            }
            
            .form-input[readonly], .form-input:disabled, textarea[readonly], select:disabled {
                background-color: rgba(17, 24, 39, 0.7);
            }
            
            #api-loader {
                background-color: rgba(17, 24, 39, 0.85);
            }
            
            #api-loader .spinner-container {
                background-color: #1f2937;
            }
            
            /* 深色模式下的特殊优化 */
            .btn {
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            }
            
            .notification {
                background-color: #065f46; /* 深色模式下的成功颜色 */
            }
            
            .order-item::after, .order-card::after {
                background-color: #3b82f6; /* 保持主色调可见性 */
                opacity: 0.2;
            }
            
            .order-item:hover::after, .order-card:hover::after {
                opacity: 1;
            }
        }
        
        /* 减少动画，提高性能模式 */
        @media (prefers-reduced-motion: reduce) {
            * {
                transition-duration: 0.01ms !important;
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
            }
            
            .btn:hover, .order-item:hover, .order-card:hover {
                transform: none !important;
            }
        }
    </style>
    <style>
        /* 设备特定样式 */
        /* 移动设备优化 */
        html.mobile .btn, html.tablet .btn {
            min-height: 44px; /* 确保触摸目标足够大 */
            padding: 0.625rem 1rem;
        }
        
        html.mobile .form-input, html.mobile textarea, html.mobile select,
        html.tablet .form-input, html.tablet textarea, html.tablet select {
            font-size: 16px; /* 防止iOS自动缩放 */
            padding: 0.625rem;
        }
        
        html.mobile #orders-list, html.tablet #orders-list {
            grid-template-columns: 1fr;
            gap: 0.75rem;
            padding: 0.75rem;
        }
        
        html.mobile .order-item, html.tablet .order-item {
            padding: 0.75rem;
        }
        
        html.mobile .notification, html.tablet .notification {
            left: 1rem;
            right: 1rem;
            bottom: 1rem;
            max-width: none;
        }
        
        /* Edge浏览器特定优化 */
        html.edge .btn {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        html.edge body {
            text-rendering: optimizeLegibility;
        }
        
        /* 减少动画，提高性能模式 */
        @media (prefers-reduced-motion: reduce) {
            * {
                transition-duration: 0.01ms !important;
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
            }
            
            .btn:hover, .order-item:hover, .order-card:hover {
                transform: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="notification" id="notification"></div>
    
    <header>
        <div class="container header-content">
            <div class="logo-container">
                <img src="images/logo.svg" alt="Logo">
                <h1>Jing Ge订单处理</h1>
            </div>
            <a href="index.html" class="btn btn-gray" data-i18n="back-to-home">返回首页</a>
        </div>
    </header>

    <main class="container">
        <div class="grid">
            <!-- 输入部分 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="jingge-original-order">Jing Ge原始订单</h2>
                    <div class="lang-selector">
                        <select id="language-selector" class="form-input" style="width: auto; min-width: 120px;" aria-label="选择语言 Select language">
                            <option value="zh">中文</option>
                            <option value="en">English</option>
                            <option value="jp">日本語</option>
                            <option value="ko">한국어</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="raw-order" class="form-label" data-i18n="original-order-data">原始订单数据</label>
                        <textarea id="raw-order" data-i18n-placeholder="paste-jingge-order-data" placeholder="粘贴Jing Ge原始订单数据..."></textarea>
                    </div>
                    <div class="button-group">
                        <button id="convert-btn" class="btn btn-primary flex-1" data-i18n="process-order">处理订单</button>
                        <button id="reset-btn" class="btn btn-gray" data-i18n="reset">重置</button>
                    </div>
                </div>
            </div>

            <!-- 输出部分 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title" data-i18n="standardized-order">标准化订单</h2>
                    <button id="copy-output" class="btn btn-gray" data-i18n="copy-output">复制</button>
                </div>
                <div class="card-body">
                    <form id="order-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="ota" class="form-label">OTA平台</label>
                                <input type="text" id="ota" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="ota-reference" class="form-label">OTA订单号</label>
                                <input type="text" id="ota-reference" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="price" class="form-label">价格</label>
                                <input type="text" id="price" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="name" class="form-label">乘客姓名</label>
                                <input type="text" id="name" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="phone" class="form-label">电话</label>
                                <input type="text" id="phone" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" id="email" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="flight-number" class="form-label">航班号</label>
                                <input type="text" id="flight-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="pickup-datetime" class="form-label">接机时间</label>
                                <input type="text" id="pickup-datetime" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="pickup-address" class="form-label">接机地址</label>
                                <input type="text" id="pickup-address" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="dropoff-address" class="form-label">送机地址</label>
                                <input type="text" id="dropoff-address" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="car-type" class="form-label">车型</label>
                                <input type="text" id="car-type" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="passenger-number" class="form-label">乘客人数</label>
                                <input type="number" id="passenger-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="luggage-number" class="form-label">行李数量</label>
                                <input type="number" id="luggage-number" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="language" class="form-label">语言</label>
                                <input type="text" id="language" class="form-input" readonly>
                            </div>
                            <div class="form-group">
                                <label for="category" class="form-label">类别</label>
                                <select id="category" class="form-input" disabled>
                                    <option value="airport">机场接送</option>
                                    <option value="Charter 包车">Charter 包车</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="subcategory" class="form-label">子类别</label>
                                <select id="subcategory" class="form-input" disabled>
                                    <option value="pickup">接机</option>
                                    <option value="dropoff">送机</option>
                                    <option value="charter">包车</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="driving-region" class="form-label">驾驶区域</label>
                                <select id="driving-region" class="form-input" disabled>
                                    <option value="kl">吉隆坡</option>
                                    <option value="penang">槟城</option>
                                    <option value="sg">新加坡</option>
                                    <option value="jb">新山</option>
                                    <option value="sabah">沙巴</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="driver" class="form-label">司机数量</label>
                                <input type="number" id="driver" class="form-input" value="1" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="remark" class="form-label">备注</label>
                            <textarea id="remark" rows="2" class="form-input" readonly></textarea>
                        </div>
                        <div class="button-group">
                            <button type="button" id="edit-btn" class="btn btn-gray flex-1">编辑</button>
                            <button type="button" id="save-btn" class="btn btn-green flex-1 hidden">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 多订单结果容器 -->
        <div id="multi-orders-container" class="card hidden">
            <div class="card-header">
                <h2 class="card-title" data-i18n="multiple-orders-detected">已识别到的多个订单</h2>
            </div>
            <div id="orders-list" class="card-body" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 1rem; padding: 1rem;">
                <!-- 订单条目将在这里动态添加 -->
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            Built with <a href="https://flowith.net" target="_blank" rel="noopener" style="color: #3b82f6; text-decoration: none;">Flowith Oracle</a>.
        </div>
    </footer>

    <script>
        // 多语言支持
        const i18n = {
            zh: {
                // 页面标题
                "jingge-original-order": "Jing Ge原始订单",
                "standardized-order": "标准化订单",
                "multiple-orders-detected": "已识别到的多个订单",
                // 表单标签和占位符
                "original-order-data": "原始订单数据",
                "paste-jingge-order-data": "粘贴Jing Ge原始订单数据...",
                "ota-platform": "OTA平台",
                "order-number": "订单编号",
                "price": "价格",
                "passenger-name": "乘客姓名",
                "phone": "电话",
                "email": "邮箱",
                "flight-number": "航班号",
                "pickup-time": "接机时间",
                "pickup-address": "接机地址",
                "dropoff-address": "送达地址",
                "car-type": "车型",
                "luggage-count": "行李数量",
                "passenger-count": "乘客人数",
                "language": "语言",
                "order-type": "订单类型",
                "driving-region": "驾驶区域",
                "driver-count": "司机数量",
                "remarks": "备注",
                // 按钮
                "process-order": "处理订单",
                "reset": "重置",
                "enable-edit": "启用编辑",
                "save-changes": "保存更改",
                "copy-output": "复制输出",
                "view-details": "查看详细",
                "copy": "复制",
                "back-to-home": "返回首页",
                // 通知
                "please-enter-data": "请输入原始订单数据",
                "data-copied": "数据已复制到剪贴板",
                "edit-enabled": "已启用编辑模式",
                "changes-saved": "更改已保存",
                "multiple-orders-found": "检测到多个订单，正在处理...",
            },
            en: {
                // Page titles
                "jingge-original-order": "Jing Ge Original Order",
                "standardized-order": "Standardized Order",
                "multiple-orders-detected": "Multiple Orders Detected",
                // Form labels and placeholders
                "original-order-data": "Original Order Data",
                "paste-jingge-order-data": "Paste Jing Ge original order data...",
                "ota-platform": "OTA Platform",
                "order-number": "Order Number",
                "price": "Price",
                "passenger-name": "Passenger Name",
                "phone": "Phone",
                "email": "Email",
                "flight-number": "Flight Number",
                "pickup-time": "Pickup Time",
                "pickup-address": "Pickup Address",
                "dropoff-address": "Dropoff Address",
                "car-type": "Car Type",
                "luggage-count": "Luggage Count",
                "passenger-count": "Passenger Count",
                "language": "Language",
                "order-type": "Order Type",
                "driving-region": "Driving Region",
                "driver-count": "Driver Count",
                "remarks": "Remarks",
                // Buttons
                "process-order": "Process Order",
                "reset": "Reset",
                "enable-edit": "Enable Edit",
                "save-changes": "Save Changes",
                "copy-output": "Copy Output",
                "view-details": "View Details",
                "copy": "Copy",
                "back-to-home": "Back to Home",
                // Notifications
                "please-enter-data": "Please enter original order data",
                "data-copied": "Data copied to clipboard",
                "edit-enabled": "Edit mode enabled",
                "changes-saved": "Changes saved",
                "multiple-orders-found": "Multiple orders detected, processing...",
                // Field values
                "airport-pickup": "Airport Pickup",
                "airport-dropoff": "Airport Dropoff",
                "charter": "Charter Service"
            },
            jp: {
                // ページタイトル
                "jingge-original-order": "Jing Ge元注文",
                "standardized-order": "標準化注文",
                "multiple-orders-detected": "検出された複数の注文",
                // フォームラベルとプレースホルダー
                "original-order-data": "元注文データ",
                "paste-jingge-order-data": "Jing Geの元注文データを貼り付け...",
                "ota-platform": "OTAプラットフォーム",
                "order-number": "注文番号",
                "price": "価格",
                "passenger-name": "乗客名",
                "phone": "電話番号",
                "email": "メール",
                "flight-number": "フライト番号",
                "pickup-time": "送迎時間",
                "pickup-address": "ピックアップ住所",
                "dropoff-address": "目的地住所",
                "car-type": "車種",
                "luggage-count": "荷物数",
                "passenger-count": "乗客数",
                "language": "言語",
                "order-type": "注文タイプ",
                "driving-region": "運転地域",
                "driver-count": "ドライバー数",
                "remarks": "備考",
                // ボタン
                "process-order": "注文処理",
                "reset": "リセット",
                "enable-edit": "編集を有効化",
                "save-changes": "変更を保存",
                "copy-output": "出力をコピー",
                "view-details": "詳細を表示",
                "copy": "コピー",
                "back-to-home": "ホームに戻る",
                // 通知
                "please-enter-data": "元注文データを入力してください",
                "data-copied": "データがクリップボードにコピーされました",
                "edit-enabled": "編集モードが有効になりました",
                "changes-saved": "変更が保存されました",
                "multiple-orders-found": "複数の注文が検出されました、処理中...",
                // フィールド値
                "airport-pickup": "空港送迎",
                "airport-dropoff": "空港へ送り",
                "charter": "チャーターサービス"
            },
            ko: {
                // 페이지 제목
                "jingge-original-order": "Jing Ge 원본 주문",
                "standardized-order": "표준화된 주문",
                "multiple-orders-detected": "감지된 다중 주문",
                // 폼 라벨 및 플레이스홀더
                "original-order-data": "원본 주문 데이터",
                "paste-jingge-order-data": "Jing Ge 원본 주문 데이터 붙여넣기...",
                "ota-platform": "OTA 플랫폼",
                "order-number": "주문 번호",
                "price": "가격",
                "passenger-name": "승객 이름",
                "phone": "전화번호",
                "email": "이메일",
                "flight-number": "항공편 번호",
                "pickup-time": "픽업 시간",
                "pickup-address": "픽업 주소",
                "dropoff-address": "하차 주소",
                "car-type": "차량 유형",
                "luggage-count": "수하물 개수",
                "passenger-count": "승객 수",
                "language": "언어",
                "order-type": "주문 유형",
                "driving-region": "운전 지역",
                "driver-count": "운전자 수",
                "remarks": "비고",
                // 버튼
                "process-order": "주문 처리",
                "reset": "재설정",
                "enable-edit": "편집 활성화",
                "save-changes": "변경사항 저장",
                "copy-output": "출력 복사",
                "view-details": "상세 보기",
                "copy": "복사",
                "back-to-home": "홈으로 돌아가기",
                // 알림
                "please-enter-data": "원본 주문 데이터를 입력하세요",
                "data-copied": "데이터가 클립보드에 복사되었습니다",
                "edit-enabled": "편집 모드가 활성화되었습니다",
                "changes-saved": "변경사항이 저장되었습니다",
                "multiple-orders-found": "다중 주문이 감지되었습니다, 처리 중...",
                // 필드 값
                "airport-pickup": "공항 픽업",
                "airport-dropoff": "공항 드랍",
                "charter": "전세 서비스"
            }
        };

        // 更新所有UI文本
        function updateUILanguage(lang) {
            // 更新标题
            document.title = `OTA Order Converter - ${i18n[lang]["jingge-original-order"]}`;
            
            // 更新data-i18n属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (i18n[lang][key]) {
                    element.textContent = i18n[lang][key];
                }
            });
            
            // 更新占位符
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (i18n[lang][key]) {
                    element.placeholder = i18n[lang][key];
                }
            });
            
            // 保存当前语言到localStorage
            localStorage.setItem('preferred-language', lang);
        }

        // 定义Jing Ge的识别规则 - 重构为支持DeepSeek API智能解析
        const jingGeRule = {
            id: 1,
            provider: 'Jing Ge',
            identification: '商铺订单, 订单号, 名字, 行程',
            fieldMappings: [
                // 金额提取：商铺订单162元 → price字段（需要乘以0.61）
                { field: 'price', pattern: '商铺订单(\\d+)元', transform: (value) => Math.round(parseFloat(value) * 0.61 * 100) / 100 },

                // 地区识别：吉隆坡接机5座车 → driving-region映射
                { field: 'driving-region', pattern: '(吉隆坡|新加坡|槟城|新山|沙巴)', transform: (value) => {
                    const regionMap = { '吉隆坡': 'kl', '新加坡': 'sg', '槟城': 'penang', '新山': 'jb', '沙巴': 'sabah' };
                    return regionMap[value] || 'kl';
                }},

                // 订单类型：接机|送机|包车|一日游|半日游|城市游览 → category/subcategory自动判断
                { field: 'service-type', pattern: '(接机|送机|包车|一日游|半日游|城市游览|包车游|市内游|观光游|旅游包车)', transform: (value) => {
                    if (value === '接机') return { category: 'airport', subcategory: 'pickup' };
                    if (value === '送机') return { category: 'airport', subcategory: 'dropoff' };
                    // 所有其他服务类型都归类为包车服务
                    return { category: 'Charter 包车', subcategory: 'charter' };
                }},

                // 车型提取：5座车|SUV|商务|豪华 → car-type标准化
                { field: 'car-type', pattern: '(\\d+座车|SUV|商务|豪华[^\\s]*)', transform: (value) => {
                    const carTypeMap = {
                        '5座车': 'sedan', '7座车': 'SUV', '商务七座': 'Serena',
                        '豪华七座': 'Alphard/Velfire', '商务九座': 'Starex', 'SUV': 'SUV'
                    };
                    return carTypeMap[value] || value;
                }},

                // OTA参考号：订单号闲鱼47474659036202889545971 或 订单号47474659036202889545971 → ota-reference字段
                { field: 'ota-reference', pattern: '订单号(?:闲鱼)?(\\d{10,25})' },

                // 航班号：航班号 (AK117) → flight-number字段
                { field: 'flight-number', pattern: '航班号\\s*\\(?([A-Z0-9]{2,7})\\)?' },

                // 客户姓名：Customer Name: jack → name字段
                { field: 'name', pattern: 'Customer Name:\\s*([^\\n]+)' },

                // 日期时间组合：日期: 2025-05-19 时间:02:10: → pickup-datetime字段
                { field: 'pickup-datetime', pattern: '日期:\\s*([^\\n]+).*?时间[:\\s]*([^\\n:]+)', transform: (date, time) => {
                    return `${date.trim()} ${time.trim().replace(':', '')}:00`;
                }},

                // 行程地址：行程:T2-吉隆坡 → pickup-address字段
                { field: 'pickup-address', pattern: '行程:\\s*([^\\n]+)' },

                // 目的地地址：地址: Zero Healthcare... → dropoff-address字段
                { field: 'dropoff-address', pattern: '地址:\\s*([^\\n]+)' },

                // 🔧 Enhancement 3: Remark 字段智能映射
                // 路线信息：马六甲一日游、布城马六甲一日游 → remark字段
                { field: 'remark', pattern: '([^\\n]*(?:一日游|半日游|包车游|城市游览|观光游|旅游)[^\\n]*)', transform: (value) => {
                    // 提取核心路线信息，去除多余的描述
                    return value.replace(/^\d+座车|7座车|包车|布城/, '').trim();
                }},

                // 行程描述：行程：马六甲一日游 → remark字段（优先级较低）
                { field: 'remark-secondary', pattern: '行程[：:\\s]*([^\\n]+)', transform: (value) => {
                    // 如果包含旅游相关关键词，提取为remark
                    if (/一日游|半日游|包车游|城市游览|观光游|旅游|景点/.test(value)) {
                        return value.trim();
                    }
                    return null; // 不符合条件则不设置
                }},

                // 特殊需求和描述：备注、说明、要求等 → remark字段
                { field: 'remark-notes', pattern: '(?:备注|说明|要求|特殊需求)[：:\\s]*([^\\n]+)' }
            ]
        };

        // 车型标准映射
        const vehicleStandard = {
            '经济，舒适 五座': 'sedan',
            '经济，舒适 七座': 'SUV',
            '商务七座': 'Serena',
            '豪华七座': 'Alphard/Velfire',
            '商务九座': 'Starex',
            '中巴十五座': 'Van'
        };

        // 翻译缓存
        const translationCache = new Map();
        
        // 性能监控变量
        let apiCallCount = 0;
        let totalProcessingTime = 0;
        let translationStats = {
            cacheHits: 0,
            apiCalls: 0,
            englishDetected: 0,
            airportKeywords: 0,
            batchTranslations: 0,
            smartSegmentations: 0
        };

        // 扩展的英文和马来语单词列表（不需要翻译）
        const englishMalayWords = new Set([
            // 英文通用词汇
            'zero', 'healthcare', 'warehouse', 'lot', 'hotel', 'resort', 'mall', 'plaza', 'tower',
            'building', 'centre', 'center', 'park', 'garden', 'square', 'street', 'road', 'avenue',
            'drive', 'lane', 'way', 'place', 'airport', 'terminal', 'international', 'domestic',
            'station', 'port', 'bridge', 'north', 'south', 'east', 'west', 'central', 'city',
            'town', 'village', 'district', 'floor', 'level', 'unit', 'block', 'wing', 'phase',
            'section', 'zone', 'office', 'shop', 'store', 'market', 'clinic', 'hospital',

            // 马来语地址常用词汇
            'jalan', 'kampung', 'baru', 'sungai', 'buloh', 'taman', 'bandar', 'shah', 'alam',
            'petaling', 'jaya', 'subang', 'damansara', 'ampang', 'cheras', 'kepong', 'sentul',
            'wangsa', 'maju', 'sri', 'bukit', 'bintang', 'utama', 'indah', 'permai', 'damai',
            'mutiara', 'permata', 'intan', 'berlian', 'delima', 'zamrud', 'nilam', 'putra',
            'puteri', 'raja', 'sultan', 'tengku', 'dato', 'datin', 'tan', 'lim', 'lee', 'wong',

            // 马来西亚地名
            'kuala', 'lumpur', 'selangor', 'penang', 'johor', 'sabah', 'sarawak', 'melaka',
            'negeri', 'sembilan', 'pahang', 'kelantan', 'terengganu', 'perlis', 'kedah',
            'putrajaya', 'labuan', 'ipoh', 'georgetown', 'alor', 'setar', 'kota', 'kinabalu',
            'kuching', 'miri', 'sibu', 'bintulu', 'sandakan', 'tawau', 'semporna',

            // 新加坡地名和词汇
            'singapore', 'changi', 'jurong', 'tampines', 'bedok', 'hougang', 'yishun', 'woodlands',
            'sembawang', 'admiralty', 'marsiling', 'toa', 'payoh', 'bishan', 'ang', 'mo', 'kio',
            'serangoon', 'punggol', 'sengkang', 'bukit', 'timah', 'clementi', 'queenstown',

            // 机场代码和航空相关
            'klia', 'klia2', 'senai', 'bayan', 'lepas', 'langkawi', 'alor', 'star', 'sultan',
            'abdul', 'aziz', 'shah', 'subang', 'skypark'
        ]);

        // 常见地址模式（完整地址缓存）
        const commonAddressPatterns = new Map([
            ['Zero Healthcare Sg Buloh Warehouse', 'Zero Healthcare Sg Buloh Warehouse'],
            ['T1-吉隆坡', 'T1-Kuala Lumpur'],
            ['T2-吉隆坡', 'T2-Kuala Lumpur'],
            ['T1-新加坡', 'T1-Singapore'],
            ['T2-新加坡', 'T2-Singapore'],
            ['吉隆坡机场', 'Kuala Lumpur International Airport'],
            ['新加坡机场', 'Changi International Airport'],
            ['槟城机场', 'Penang International Airport']
        ]);

        // 本地翻译映射表（扩展版）
        const localTranslations = {
            // 马来西亚吉隆坡
            '吉隆坡武吉免登世民酒店': 'CitizenM Kuala Lumpur Bukit Bintang',
            '吉隆坡帝盛酒店': 'Dorsett Kuala Lumpur',
            '洲际酒店': 'InterContinental Kuala Lumpur',
            '吉隆坡索菲特': 'Sofitel Kuala Lumpur Damansara',
            '吉隆坡雅乐轩': 'Aloft Kuala Lumpur Sentral',
            '吉隆坡福朋喜来登': 'Four Points by Sheraton Kuala Lumpur',
            '吉隆坡双子塔阳光广场酒店': 'KLCC Sunshine Hotel',
            '吉隆坡悦榕庄': 'Banyan Tree Kuala Lumpur',
            '吉隆坡瑞吉酒店': 'The St. Regis Kuala Lumpur',
            '吉隆坡文华东方': 'Mandarin Oriental Kuala Lumpur',
            '吉隆坡盛贸饭店': 'Traders Hotel Kuala Lumpur',
            '吉隆坡大华酒店': 'The Majestic Hotel Kuala Lumpur',
            '吉隆坡希尔顿': 'Hilton Kuala Lumpur',
            '吉隆坡JW万豪': 'JW Marriott Hotel Kuala Lumpur',
            '吉隆坡丽思卡尔顿': 'The Ritz-Carlton Kuala Lumpur',
            '吉隆坡香格里拉': 'Shangri-La Hotel Kuala Lumpur',
            '吉隆坡铂尔曼酒店': 'Pullman Kuala Lumpur City Centre',
            '吉隆坡艾美酒店': 'Le Méridien Kuala Lumpur',
            '吉隆坡威斯汀酒店': 'The Westin Kuala Lumpur',
            '吉隆坡千禧大酒店': 'Grand Millennium Kuala Lumpur',
            '吉隆坡文华东方酒店': 'Mandarin Oriental, Kuala Lumpur',
            '吉隆坡君悦酒店': 'Grand Hyatt Kuala Lumpur',
            '吉隆坡香格里拉大酒店': 'Shangri-La Kuala Lumpur',
            '吉隆坡四季酒店': 'Four Seasons Hotel Kuala Lumpur',
            '吉隆坡丽思卡尔顿酒店': 'The Ritz-Carlton, Kuala Lumpur',
            '吉隆坡JW万豪酒店': 'JW Marriott Kuala Lumpur',
            '吉隆坡威斯汀酒店': 'The Westin Kuala Lumpur',
            '吉隆坡柏威年酒店 (悦榕庄管理)': 'Pavilion Hotel Kuala Lumpur Managed by Banyan Tree',
            '吉隆坡宾乐雅臻选酒店': 'PARKROYAL COLLECTION Kuala Lumpur',
            '吉隆坡市中心普尔曼酒店及公寓': 'Pullman Kuala Lumpur City Centre Hotel & Residences',
            '吉隆坡悦榕庄': 'Banyan Tree Kuala Lumpur',
            '吉隆坡EQ酒店': 'EQ Kuala Lumpur',
            '吉隆坡 W 酒店': 'W Kuala Lumpur',
            '吉隆坡盛贸饭店': 'Traders Hotel, Kuala Lumpur',
            '吉隆坡Impiana KLCC酒店': 'Impiana KLCC Hotel',
            '吉隆坡洲际酒店': 'InterContinental Kuala Lumpur',
            '吉隆坡希尔顿逸林酒店': 'DoubleTree by Hilton Hotel Kuala Lumpur',
            '吉隆坡希尔顿酒店': 'Hilton Kuala Lumpur',
            '吉隆坡艾美酒店': 'Le Méridien Kuala Lumpur',
            '吉隆坡瑞吉酒店': 'The St. Regis Kuala Lumpur',
            '吉隆坡中环广场雅乐轩酒店': 'Aloft Kuala Lumpur Sentral',
            '吉隆坡大华酒店, 傲途格精选': 'The Majestic Hotel Kuala Lumpur, Autograph Collection',
            '吉隆坡条纹酒店, 傲途格精选': 'Hotel Stripes Kuala Lumpur, Autograph Collection',
            '吉隆坡成功时代广场酒店': 'Berjaya Times Square Hotel, Kuala Lumpur',
            '吉隆坡美利亚酒店': 'Melia Kuala Lumpur',
            '吉隆坡帝盛酒店': 'Dorsett Kuala Lumpur',
            '吉隆坡富丽华武吉免登酒店': 'Furama Bukit Bintang',
            '吉隆坡邵氏广场美居酒店': 'Mercure Kuala Lumpur Shaw Parade',
            '吉隆坡市中心诺富特酒店': 'Novotel Kuala Lumpur City Centre',
            '吉隆坡市中心宜必思酒店': 'ibis Kuala Lumpur City Centre',
            '吉隆坡万丽酒店': 'Renaissance Kuala Lumpur Hotel & Convention Centre',
            '吉隆坡帝国喜来登酒店': 'Sheraton Imperial Kuala Lumpur Hotel',
            '吉隆坡双威太子大酒店': 'Sunway Putra Hotel Kuala Lumpur',
            '吉隆坡雅诗阁酒店公寓': 'Ascott Kuala Lumpur',
            '吉隆坡辉盛庭国际公寓': 'Fraser Place Kuala Lumpur',
            '吉隆坡宾乐雅服务式套房酒店': 'PARKROYAL Serviced Suites Kuala Lumpur',
            '武吉免登辉盛凯贝丽酒店': 'Capri by Fraser, Bukit Bintang',
            '吉隆坡源宿酒店': 'Element Kuala Lumpur',
            '吉隆坡孟沙阿丽拉酒店': 'Alila Bangsar Kuala Lumpur',
            '吉隆坡唐人街福朋喜来登酒店': 'Four Points by Sheraton Kuala Lumpur, Chinatown',
            '吉隆坡武吉免登世民酒店': 'citizenM Kuala Lumpur Bukit Bintang',
            '秋杰 - 奥蒙德酒店': 'The Chow Kit - an Ormond Hotel',
            '吉隆坡廓思酒店': 'Corus Hotel Kuala Lumpur',
            '吉隆坡瑞士花园酒店武吉免登': 'Swiss-Garden Hotel Bukit Bintang Kuala Lumpur',
            '吉隆坡市中心旅客之家酒店': 'Travelodge City Centre, Kuala Lumpur',
            '吉隆坡菲斯酒店': 'THE FACE Suites Kuala Lumpur',
            '吉隆坡 Oasia 套房酒店': 'Oasia Suites Kuala Lumpur by Far East Hospitality',
            '吉隆坡市中心智选假日酒店': 'Holiday Inn Express Kuala Lumpur City Centre',
            '吉隆坡逸兰美居酒店公寓': 'Mercure Living Kuala Lumpur Fraser Residence',
            '吉隆坡孟沙温德姆至尊酒店': 'Wyndham Grand Bangsar Kuala Lumpur',
            '吉隆坡谷中城希提特尔酒店': 'Cititel Mid Valley Kuala Lumpur',
            '吉隆坡花园 St Giles 署名酒店及公寓': 'The Gardens – A St Giles Signature Hotel & Residences',
            '吉隆坡皇家朱兰酒店': 'Royale Chulan Kuala Lumpur',
            '吉隆坡凯煌酒店': 'Concorde Hotel Kuala Lumpur',
            '吉隆坡安莎酒店': 'Ansa Hotel Kuala Lumpur',
            '吉隆坡皇家酒店': 'Hotel Royal Kuala Lumpur',
            '吉隆坡日志酒店': 'The Kuala Lumpur Journal Hotel',
            '吉隆坡 KLoé 酒店': 'KLoé Hotel',
            '吉隆坡龙城世家武吉锡兰服务式公寓': 'Lanson Place Bukit Ceylon Serviced Residences',
            '吉隆坡泛太平洋服务套房酒店': 'Pan Pacific Serviced Suites Kuala Lumpur',
            '吉隆坡盛捷服务公寓': 'Somerset Kuala Lumpur',
            '吉隆坡中环雅诗阁服务公寓': 'Ascott Sentral Kuala Lumpur',
            '吉隆坡满家乐凯悦嘉寓酒店': 'Hyatt House Kuala Lumpur, Mont Kiara',
            '吉隆坡白沙罗索菲特酒店': 'Sofitel Kuala Lumpur Damansara',
            '吉隆坡 Jalan TAR North 希尔顿花园酒店': 'Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman North',
            '吉隆坡市中心华美达酒店': 'Ramada by Wyndham Kuala Lumpur City Centre',
            '吉隆坡弗雷泽商业园宜必思尚品酒店': 'ibis Styles Kuala Lumpur Fraser Business Park',
            '吉隆坡AC万豪酒店': 'AC Hotel by Marriott Kuala Lumpur',
            '吉隆坡双威伟乐酒店': 'Sunway Velocity Hotel Kuala Lumpur',
            '天井酒店': 'Tian Jing Hotel',
            '灯笼酒店': 'Lantern Hotel',
            '吉隆坡中央市场太平洋快捷酒店': 'Pacific Express Hotel Central Market Kuala Lumpur',
            '吉隆坡中转酒店': 'Hotel Transit Kuala Lumpur',
            '吉隆坡棉兰端姑普雷斯科特酒店': 'Prescott Hotel Kuala Lumpur Medan Tuanku',
            '吉隆坡PNB派乐纳福朋酒店及套房': 'PNB Perdana Hotel & Suites On The Park Kuala Lumpur',
            '吉隆坡 Expressionz 专业套房': 'Expressionz Professional Suites', 
            '吉隆坡 Ceylonz 套房': 'Ceylonz Suites Bukit Ceylon', 
            '万达镇艾凡特酒店': 'Avante Hotel', 
            '八打灵再也阿玛达酒店': 'Armada Petaling Jaya',

            // 马来西亚仙本那
            '仙本那灯塔酒店': 'Semporna Lighthouse Hotel',
            '仙本那新佳马达潜水度假村': 'Sipadan-Kapalai Dive Resort',
            '仙本那海丰精品大酒店': 'Seafest Boutique Hotel Semporna',
            '仙本那马布岛水上屋': 'Mabul Water Bungalows',
            '仙本那卡帕莱度假村': 'Kapalai Dive Resort',
            '仙本那婆罗洲潜水度假村': 'Borneo Divers Mabul Resort',
            '仙本那西巴丹水上屋': 'Sipadan Water Village Resort',
            '仙本那诗巴丹度假村': 'Sipadan Resort',
            '仙本那邦邦岛白珍珠度假村': 'Pompom Island White Pearl Resort',
            '仙本那海丰大酒店': 'Seafest Hotel Semporna',
            '仙本那诗巴丹2号酒店': 'Sipadan Inn 2', 
            '仙本那龙门客栈': 'Dragon Inn Semporna', 
            '仙本那方块床位栈': 'Cube Bed Station', 
            '仙本那Paly酒店': 'Paly Hotel Semporna', 
            '仙本那绿世界酒店': 'Green World Hotel', 
            '仙本那汉宫酒店': 'Han Palace Hotel Semporna', 
            '仙本那海洋旅馆': 'Ocean Inn', 
            '仙本那格雷斯酒店': 'Grace Hotel Semporna', 
            '仙本那记忆精品酒店': 'Memory Boutique Hotel', 
            '仙本那尚思酒店': 'Sense Hotel Semporna', 
            '仙本那遗产酒店': 'Heritage Hotel', 
            '仙本那诗巴丹1号酒店': 'Sipadan Inn 1',
            '仙本那 Ang Lee 假日住宿': 'Ang Lee Holiday Stay', 
            '仙本那城市旅馆': 'City Inn Semporna', 
            '仙本那TD珍珠酒店': 'TD Mutiara Hotel', 
            '仙本那假日潜水客栈': 'Holiday Dive Inn', 
            '仙本那婆罗洲环球诗巴丹背包客栈': 'Borneo Global Sipadan Backpackers', 
            '仙本那 Chan Living': 'Chan Living', 
            '仙本那海洋旅游中心酒店': 'Ocean Tourism Center Hotel', 
            '仙本那My Inn酒店': 'My Inn Hotel Semporna', 
            '仙本那浪景酒店': 'The Wave View Hotel', 
            '仙本那Wai Lai民宿': 'Wai Lai Pension', 
            '仙本那露娜旅馆': 'Luna Guesthouse', 
            '仙本那 Arung Hayat 酒店': 'Arung Hayat Semporna',
            '仙本那 永达大酒店': 'Wing Tat Grand Hotel Semporna',
            
            // 马来西亚槟城
            '槟城香格里拉沙洋度假酒店': 'Shangri-La\'s Rasa Sayang Resort Penang',
            '槟城东方大酒店': 'Eastern & Oriental Hotel Penang',
            '槟城硬石酒店': 'Hard Rock Hotel Penang',
            '槟城G酒店': 'G Hotel Gurney',
            '槟城双威酒店': 'Sunway Hotel Georgetown',
            '槟城宾乐雅酒店': 'Parkroyal Penang Resort',
            '槟城彩虹天堂海滩度假村': 'Rainbow Paradise Beach Resort Penang',
            '槟城皇家酒店': 'Royale Chulan Penang',
            
            // 新加坡
            '新加坡卡尔顿城市酒店': 'Carlton City Hotel Singapore',
            'Novotel Living Singapore Orchard': 'Novotel Living Singapore Orchard',
            'Four Points by Sheraton Singapore, Riverview': 'Four Points by Sheraton Singapore, Riverview',
            '圣淘沙名胜世界-欧芮酒店': 'Hotel Ora Sentosa',
            '新加坡金沙酒店': 'Marina Bay Sands Singapore',
            '新加坡香格里拉': 'Shangri-La Hotel Singapore',
            '新加坡丽思卡尔顿美年': 'The Ritz-Carlton Millenia Singapore',
            '新加坡圣淘沙名胜世界': 'Resorts World Sentosa',
            '新加坡康莱德酒店': 'Conrad Centennial Singapore',
            '新加坡乌节大酒店': 'Orchard Hotel Singapore',
            '新加坡泛太平洋酒店': 'Pan Pacific Singapore',
            '新加坡费尔蒙酒店': 'Fairmont Singapore',
            '新加坡瑞士史丹佛酒店': 'Swissôtel The Stamford Singapore',
            '新加坡滨海湾金沙酒店': 'Marina Bay Sands',
            '新加坡浮尔顿酒店': 'The Fullerton Hotel Singapore',
            '新加坡丽思卡尔顿美年酒店': 'The Ritz-Carlton, Millenia Singapore',
            '新加坡文华东方酒店': 'Mandarin Oriental, Singapore',
            '新加坡泛太平洋酒店': 'Pan Pacific Singapore',
            '新加坡滨海湾宾乐雅臻选酒店': 'PARKROYAL COLLECTION Marina Bay, Singapore',
            '新加坡南岸JW万豪酒店': 'JW Marriott Hotel Singapore South Beach',
            '新加坡香格里拉大酒店': 'Shangri-La Singapore',
            '新加坡四季酒店': 'Four Seasons Hotel Singapore',
            '新加坡乌节希尔顿酒店': 'Hilton Singapore Orchard',
            '新加坡董厦万豪酒店': 'Singapore Marriott Tang Plaza Hotel',
            '新加坡君悦酒店': 'Grand Hyatt Singapore',
            '新加坡乌节路YOTEL酒店': 'YOTEL Singapore Orchard Road',
            '新加坡乌节门JEN酒店 (香格里拉集团)': 'JEN Singapore Orchardgateway by Shangri-La',
            '新加坡史各士皇族酒店': 'Royal Plaza on Scotts',
            '新加坡良木园酒店': 'Goodwood Park Hotel',
            
            // --- 圣淘沙岛 (Sentosa Island) ---
            '新加坡圣淘沙名胜世界 - 逸濠酒店': 'Resorts World Sentosa - Equarius Hotel',
            '新加坡圣淘沙名胜世界 - 迈克尔酒店': 'Resorts World Sentosa - Hotel Michael',
            '新加坡嘉佩乐酒店': 'Capella Singapore',
            '新加坡香格里拉圣淘沙度假酒店': 'Shangri-La Rasa Sentosa, Singapore',
            '新加坡圣淘沙湾 W 酒店': 'W Singapore - Sentosa Cove',
            '新加坡圣淘沙索菲特水疗度假酒店': 'Sofitel Singapore Sentosa Resort & Spa',

            // --- 市政厅 / 克拉码头 / 武吉士 (City Hall / Clarke Quay / Bugis) ---
            '新加坡莱佛士酒店': 'Raffles Hotel Singapore',
            '新加坡史丹福瑞士酒店': 'Swissôtel The Stamford',
            '新加坡费尔蒙酒店': 'Fairmont Singapore',
            '新加坡洲际酒店': 'InterContinental Singapore',
            '新加坡卡尔登酒店': 'Carlton Hotel Singapore',
            '新加坡克拉码头百乐商业酒店': 'Paradox Singapore Merchant Court at Clarke Quay',

            // --- 其他区域 (Chinatown / Downtown etc.) ---
            '新加坡皮克林宾乐雅臻选酒店': 'PARKROYAL COLLECTION Pickering, Singapore',
            '新加坡市中心奥西亚酒店 (远东酒店集团)': 'Oasia Hotel Downtown, Singapore by Far East Hospitality',
            '新加坡市中豪亚酒店 (远东酒店集团)': 'The Clan Hotel Singapore by Far East Hospitality',

            // 特殊酒店名称映射（解决字面翻译问题）
            '莱恩酒店': 'Sleeping Lion Hotel',
            '莱恩套房酒店': 'Sleeping Lion Suites',
            '睡狮酒店': 'Sleeping Lion Hotel',
            '睡狮套房': 'Sleeping Lion Suites',
            '金狮酒店': 'Golden Lion Hotel',
            '银狮酒店': 'Silver Lion Hotel',
            '白狮酒店': 'White Lion Hotel',
            '红狮酒店': 'Red Lion Hotel',
            '蓝狮酒店': 'Blue Lion Hotel',
            '绿狮酒店': 'Green Lion Hotel',
            '黑狮酒店': 'Black Lion Hotel',
            '狮城酒店': 'Lion City Hotel',
            '狮王酒店': 'Lion King Hotel',
            '雄狮酒店': 'Majestic Lion Hotel',
            '威狮酒店': 'Mighty Lion Hotel',
            '皇狮酒店': 'Royal Lion Hotel',
            '帝狮酒店': 'Imperial Lion Hotel',
            '凤凰酒店': 'Phoenix Hotel',
            '龙凤酒店': 'Dragon Phoenix Hotel',
            '金凤酒店': 'Golden Phoenix Hotel',
            '银凤酒店': 'Silver Phoenix Hotel',
            '红凤酒店': 'Red Phoenix Hotel',
            '蓝凤酒店': 'Blue Phoenix Hotel',
            '翡翠酒店': 'Jade Hotel',
            '珍珠酒店': 'Pearl Hotel',
            '钻石酒店': 'Diamond Hotel',
            '水晶酒店': 'Crystal Hotel',
            '宝石酒店': 'Gem Hotel',
            '黄金酒店': 'Golden Hotel',
            '白银酒店': 'Silver Hotel',
            '青铜酒店': 'Bronze Hotel',
            '铂金酒店': 'Platinum Hotel',
            '皇冠酒店': 'Crown Hotel',
            '王冠酒店': 'Royal Crown Hotel',
            '帝王酒店': 'Imperial Hotel',
            '皇家酒店': 'Royal Hotel',
            '贵族酒店': 'Noble Hotel',
            '公爵酒店': 'Duke Hotel',
            '伯爵酒店': 'Earl Hotel',
            '侯爵酒店': 'Marquis Hotel',
            '男爵酒店': 'Baron Hotel',
            '骑士酒店': 'Knight Hotel',
            '勇士酒店': 'Warrior Hotel',
            '英雄酒店': 'Hero Hotel',
            '传奇酒店': 'Legend Hotel',
            '神话酒店': 'Myth Hotel',
            '史诗酒店': 'Epic Hotel',
            '经典酒店': 'Classic Hotel',
            '精品酒店': 'Boutique Hotel',
            '豪华酒店': 'Luxury Hotel',
            '尊贵酒店': 'Premium Hotel',
            '至尊酒店': 'Supreme Hotel',
            '顶级酒店': 'Top Hotel',
            '一流酒店': 'First Class Hotel',
            '五星酒店': 'Five Star Hotel',
            '六星酒店': 'Six Star Hotel',
            '七星酒店': 'Seven Star Hotel',

            // ========================================
            // COMPREHENSIVE HOTEL DATABASE (300+ Properties)
            // ========================================

            // CRITICAL FIXES - HIGHEST PRIORITY
            '莱恩酒店': 'Sleeping Lion Hotel',                    // CRITICAL: NOT "Lane Hotel"
            '莱恩套房酒店': 'Sleeping Lion Suites',               // CRITICAL: NOT "Lane Suites Hotel"
            '睡狮酒店': 'Sleeping Lion Hotel',                    // Alternative Chinese name
            '睡狮套房': 'Sleeping Lion Suites',                   // Alternative Chinese name

            // Singapore iconic landmarks
            '滨海湾金沙': 'Marina Bay Sands',                     // CRITICAL: NOT "Marina Bay Gold Sand"
            '金沙酒店': 'Marina Bay Sands',                       // CRITICAL: NOT "Gold Sand Hotel"
            '富丽敦酒店': 'The Fullerton Hotel Singapore',        // Full official name
            '莱佛士酒店': 'Raffles Singapore',                    // Official brand name
            '圣淘沙名胜世界': 'Resorts World Sentosa',            // Integrated resort name

            // Penang heritage hotels
            '东方大酒店': 'Eastern & Oriental Hotel',             // CRITICAL: NOT "Eastern Grand Hotel"
            '槟城东方大酒店': 'Eastern & Oriental Hotel Penang',   // Full location name

            // KUALA LUMPUR HOTELS
            '吉隆坡文华东方酒店': 'Mandarin Oriental Kuala Lumpur',
            '吉隆坡香格里拉酒店': 'Shangri-La Hotel Kuala Lumpur',
            '吉隆坡丽思卡尔顿': 'The Ritz-Carlton Kuala Lumpur',
            '吉隆坡万豪酒店': 'JW Marriott Hotel Kuala Lumpur',
            '吉隆坡希尔顿酒店': 'Hilton Kuala Lumpur',
            '君悦酒店': 'Grand Hyatt Kuala Lumpur',
            '洲际酒店': 'InterContinental Kuala Lumpur',
            '皇冠假日酒店': 'Crowne Plaza Kuala Lumpur',
            '假日酒店': 'Holiday Inn Kuala Lumpur',
            '喜来登酒店': 'Sheraton Imperial Kuala Lumpur',
            '威斯汀酒店': 'The Westin Kuala Lumpur',
            '四季酒店': 'Four Seasons Hotel Kuala Lumpur',
            '半岛酒店': 'The Peninsula Kuala Lumpur',
            '康拉德酒店': 'Conrad Kuala Lumpur',

            // Local Heritage & Boutique KL
            '金狮酒店': 'Golden Lion Hotel',
            '银狮酒店': 'Silver Lion Hotel',
            '白狮酒店': 'White Lion Hotel',
            '红狮酒店': 'Red Lion Hotel',
            '皇家酒店': 'Royal Hotel Kuala Lumpur',
            '帝王酒店': 'Imperial Hotel Kuala Lumpur',
            '皇冠酒店': 'Crown Hotel Kuala Lumpur',
            '翡翠酒店': 'Jade Hotel Kuala Lumpur',
            '珍珠酒店': 'Pearl International Hotel',
            '水晶酒店': 'Crystal Crown Hotel',
            '钻石酒店': 'Diamond Hotel Kuala Lumpur',
            '凤凰酒店': 'Phoenix Hotel Kuala Lumpur',
            '龙凤酒店': 'Dragon Phoenix Hotel',

            // PENANG HOTELS
            '东方酒店': 'Eastern & Oriental Hotel',
            '殖民地酒店': 'Colonial Hotel Georgetown',
            '遗产酒店': 'Heritage Hotel Georgetown',
            '古迹酒店': 'Historic Hotel Penang',
            '世遗酒店': 'UNESCO Heritage Hotel',
            '乔治市酒店': 'Georgetown Hotel',
            '香格里拉度假村': 'Shangri-La\'s Rasa Sayang Resort & Spa',
            '槟城香格里拉度假村': 'Shangri-La\'s Rasa Sayang Resort & Spa',
            '拉萨阳光度假村': 'Shangri-La\'s Rasa Sayang Resort & Spa',
            '金沙度假村': 'Shangri-La\'s Golden Sands Resort',
            '硬石酒店': 'Hard Rock Hotel Penang',
            '摇滚酒店': 'Hard Rock Hotel Penang',
            '假日度假村': 'Holiday Inn Resort Penang',
            '槟城假日度假村': 'Holiday Inn Resort Penang',
            '海景酒店': 'Bayview Hotel Georgetown',
            '槟城海景酒店': 'Bayview Hotel Georgetown',
            '湾景酒店': 'Bayview Hotel Georgetown',
            '海滨酒店': 'Waterfront Hotel Penang',
            '沙滩酒店': 'Beach Hotel Penang',
            '椰林酒店': 'Palm Beach Hotel',
            '日落酒店': 'Sunset Beach Resort',

            // SINGAPORE HOTELS
            '新加坡莱佛士酒店': 'Raffles Singapore',
            '新加坡富丽敦酒店': 'The Fullerton Hotel Singapore',
            '富丽敦湾酒店': 'The Fullerton Bay Hotel Singapore',
            '滨海湾金沙酒店': 'Marina Bay Sands',
            '圣淘沙酒店': 'Resorts World Sentosa',
            '名胜世界酒店': 'Resorts World Sentosa',
            '新加坡文华东方': 'Mandarin Oriental Singapore',
            '新加坡香格里拉': 'Shangri-La Hotel Singapore',
            '千禧丽思卡尔顿': 'The Ritz-Carlton Millenia Singapore',
            '南海滩万豪酒店': 'JW Marriott Hotel Singapore South Beach',
            '乌节希尔顿酒店': 'Hilton Singapore Orchard',
            '新加坡洲际酒店': 'InterContinental Singapore',
            '百年康拉德酒店': 'Conrad Centennial Singapore',
            '新加坡威斯汀酒店': 'The Westin Singapore',
            '新加坡四季酒店': 'Four Seasons Hotel Singapore',
            '新加坡半岛酒店': 'The Peninsula Singapore',
            '乌节路酒店': 'Orchard Hotel Singapore',
            '新加坡乌节酒店': 'Orchard Hotel Singapore',
            '购物酒店': 'Shopping Hotel Orchard',
            '精品酒店': 'Boutique Hotel Orchard',

            // KOTA KINABALU HOTELS
            '香格里拉丹绒亚路度假村': 'Shangri-La\'s Tanjung Aru Resort & Spa',
            '丹绒亚路香格里拉': 'Shangri-La\'s Tanjung Aru Resort & Spa',

            // SPECIFIC HOTEL MAPPING - Shangri-La's Rasa Ria Resort (High Priority)
            '莎莉亚香格里拉': 'Shangri-La\'s Rasa Ria Resort, Kota Kinabalu',
            '香格里拉莎莉亚': 'Shangri-La\'s Rasa Ria Resort, Kota Kinabalu',
            '莎莉亚度假村': 'Shangri-La\'s Rasa Ria Resort, Kota Kinabalu',
            '拉莎莉亚香格里拉': 'Shangri-La\'s Rasa Ria Resort, Kota Kinabalu',
            '拉莎莉亚度假村': 'Shangri-La\'s Rasa Ria Resort, Kota Kinabalu',
            'Rasa Ria': 'Shangri-La\'s Rasa Ria Resort, Kota Kinabalu',
            'Rasa Ria Shangri-La': 'Shangri-La\'s Rasa Ria Resort, Kota Kinabalu',
            'Shangri-La Rasa Ria': 'Shangri-La\'s Rasa Ria Resort, Kota Kinabalu',

            '苏特拉港度假村': 'The Magellan Sutera Resort',
            '麦哲伦苏特拉度假村': 'The Magellan Sutera Resort',
            '太平洋苏特拉度假村': 'The Pacific Sutera Hotel',
            '凯悦丽晶酒店': 'Hyatt Regency Kinabalu',
            '亚庇凯悦丽晶': 'Hyatt Regency Kinabalu',
            '亚庇万豪酒店': 'Kota Kinabalu Marriott Hotel',
            '亚庇希尔顿酒店': 'Hilton Kota Kinabalu',
            '亚庇美丽华酒店': 'Le Méridien Kota Kinabalu',
            '神山酒店': 'Mount Kinabalu Hotel',
            '京那巴鲁山酒店': 'Mount Kinabalu Hotel',
            '山景酒店': 'Mountain View Hotel KK',
            '登山酒店': 'Climber Hotel KK',
            '探险酒店': 'Adventure Hotel Kota Kinabalu',
            '沙巴酒店': 'Sabah Hotel',
            '婆罗洲酒店': 'Borneo Hotel Kota Kinabalu',
            '文化酒店': 'Cultural Hotel KK',

            // SEMPORNA HOTELS
            '西巴丹水上村庄度假村': 'Sipadan Water Village Resort',
            '水上村庄度假村': 'Sipadan Water Village Resort',
            '西巴丹度假村': 'Sipadan Resort',
            '马布岛度假村': 'Mabul Water Bungalows',
            '马布水上屋': 'Mabul Water Bungalows',
            '卡帕莱度假村': 'Kapalai Dive Resort',
            '卡帕莱水上屋': 'Kapalai Dive Resort',
            '马达京度假村': 'Mataking Island Resort',
            '马达京岛度假村': 'Mataking Island Resort',
            '邦邦岛度假村': 'Pom Pom Island Resort',
            '潜水天堂度假村': 'Diving Paradise Resort',
            '海底世界度假村': 'Underwater World Resort',
            '珊瑚花园度假村': 'Coral Garden Resort',
            '海洋生物度假村': 'Marine Life Resort',
            '仙本那酒店': 'Semporna Hotel',
            '仙本那海洋旅游中心': 'Semporna Ocean Tourism Centre',
            '海洋旅游中心': 'Ocean Tourism Centre',
            '龙门客栈': 'Dragon Inn Floating Resort',
            '龙门度假村': 'Dragon Inn Floating Resort',
            '海上客栈': 'Seafest Hotel',
            '海丰酒店': 'Seafest Hotel',
            '潜水客栈': 'Scuba Junkie Lodge',

            // JOHOR BAHRU HOTELS
            '逸林希尔顿酒店': 'DoubleTree by Hilton Hotel Johor Bahru',
            '新山逸林希尔顿': 'DoubleTree by Hilton Hotel Johor Bahru',
            '希尔顿逸林酒店': 'DoubleTree by Hilton Hotel Johor Bahru',
            '万丽酒店': 'Renaissance Johor Bahru Hotel',
            '新山万丽酒店': 'Renaissance Johor Bahru Hotel',
            '万豪万丽酒店': 'Renaissance Johor Bahru Hotel',
            '蓟酒店': 'Thistle Johor Bahru',
            '新山蓟酒店': 'Thistle Johor Bahru',
            '新山水晶皇冠': 'Crystal Crown Hotel Johor Bahru',
            '新山假日酒店': 'Holiday Inn Johor Bahru',
            '边境酒店': 'Causeway Bay Hotel',
            '关口酒店': 'Checkpoint Hotel JB',
            '过境酒店': 'Transit Hotel Johor Bahru',
            '长堤酒店': 'Causeway Hotel JB',
            '新柔长堤酒店': 'Singapore-Johor Causeway Hotel',
            '国际酒店': 'International Hotel JB',
            '购物中心酒店': 'Shopping Mall Hotel JB',
            '商场酒店': 'Mall Hotel Johor Bahru',
            '城市广场酒店': 'City Square Hotel',
            '新山城市广场酒店': 'JB City Square Hotel',
            '时尚酒店': 'Fashion Hotel Johor Bahru',
            '家庭酒店': 'Family Hotel Johor Bahru',
            '主题公园酒店': 'Theme Park Hotel Johor Bahru',
            '乐高乐园酒店': 'Legoland Hotel Malaysia',
            '度假酒店': 'Resort Hotel JB',

            // INTERNATIONAL BRAND STANDARDIZATION
            '万豪酒店': 'Marriott Hotel',
            '希尔顿酒店': 'Hilton Hotel',
            '凯悦酒店': 'Hyatt Hotel',
            '洲际酒店': 'InterContinental Hotel',
            '喜来登酒店': 'Sheraton Hotel',
            '威斯汀酒店': 'Westin Hotel',
            '万丽酒店': 'Renaissance Hotel',
            '万怡酒店': 'Courtyard by Marriott',
            '逸林酒店': 'DoubleTree by Hilton',
            '康拉德酒店': 'Conrad Hotel',
            '丽思卡尔顿酒店': 'The Ritz-Carlton',
            '文华东方酒店': 'Mandarin Oriental Hotel',
            '香格里拉酒店': 'Shangri-La Hotel',
            '四季酒店': 'Four Seasons Hotel',
            '半岛酒店': 'The Peninsula Hotel',

            // GENERIC HOTEL TYPES
            '度假村': 'Resort',
            '度假酒店': 'Resort Hotel',
            '海滩度假村': 'Beach Resort',
            '山景度假村': 'Mountain Resort',
            '温泉度假村': 'Spa Resort',
            '高尔夫度假村': 'Golf Resort',
            '套房酒店': 'Suites Hotel',
            '服务式公寓': 'Serviced Apartments',
            '公寓酒店': 'Aparthotel',
            '商务酒店': 'Business Hotel',
            '机场酒店': 'Airport Hotel',
            '市中心酒店': 'City Centre Hotel',
            '海滨酒店': 'Waterfront Hotel',
            '会议中心酒店': 'Convention Centre Hotel',
            '豪华酒店': 'Luxury Hotel',
            '经济酒店': 'Economy Hotel',
            '预算酒店': 'Budget Hotel',
            '背包酒店': 'Backpacker Hotel'
        };

        // 本地模糊匹配函数
        function fuzzyMatchLocal(text) {
            // 精确匹配
            if (localTranslations[text]) {
                return localTranslations[text];
            }
            
            // 包含匹配
            for (const [key, value] of Object.entries(localTranslations)) {
                if (text.includes(key)) {
                    return text.replace(key, value);
                }
            }
            
            // 反向包含匹配（文本是键的一部分）
            for (const [key, value] of Object.entries(localTranslations)) {
                if (key.includes(text)) {
                    return value;
                }
            }
            
            return null;
        }

        // 修改后的翻译函数（更新为使用Gemini API）
        async function translateWithCache(text) {
            if (!text || text.trim() === '') return text;

            // 1. 优先检查缓存
            if (translationCache.has(text)) {
                translationStats.cacheHits++;
                return translationCache.get(text);
            }

            // 2. 尝试本地模糊匹配
            const localMatch = fuzzyMatchLocal(text);
            if (localMatch) {
                translationCache.set(text, localMatch);
                return localMatch;
            }

            // 3. 尝试使用 Gemini API 查询（根据用户偏好）
            try {
                console.log(`本地翻译未找到匹配，尝试使用 Gemini API 查询: ${text}`);
                apiCallCount++;
                translationStats.apiCalls++;

                const geminiResult = await queryAddressEnglishNameWithGemini(text);

                if (geminiResult && geminiResult !== text) {
                    console.log(`Gemini API 查询成功: ${text} -> ${geminiResult}`);
                    translationCache.set(text, geminiResult);
                    return geminiResult;
                }
            } catch (error) {
                console.error(`Gemini API 查询失败: ${error.message}`);
            }

            // 4. 所有方法都失败，返回原文
            return text;
        }

        /**
         * @function isEnglishMalayAddress - 检测地址是否已经是英文或马来语
         * @param {string} address - 地址字符串
         * @returns {boolean} 是否为英文/马来语地址
         */
        function isEnglishMalayAddress(address) {
            if (!address || address.trim() === '') return false;

            console.log(`🔍 检测地址语言: "${address}"`);

            // 检查是否包含中文字符
            const hasChinese = /[\u4e00-\u9fa5]/.test(address);
            if (!hasChinese) {
                console.log(`✅ 无中文字符，判定为英文/马来语地址`);
                translationStats.englishDetected++;
                return true;
            }

            // 分析单词组成
            const words = address.toLowerCase()
                .replace(/[,\.\(\)\[\]]/g, ' ')  // 替换标点符号为空格
                .split(/[\s\-\/]+/)              // 按空格、连字符、斜杠分割
                .filter(word => word.length > 0); // 过滤空字符串

            console.log(`📝 分析单词: [${words.join(', ')}]`);

            // 计算英文/马来语单词比例
            const knownWords = words.filter(word => englishMalayWords.has(word));
            const knownWordRatio = words.length > 0 ? knownWords.length / words.length : 0;

            console.log(`📊 已知单词: [${knownWords.join(', ')}]`);
            console.log(`📊 已知单词比例: ${knownWordRatio.toFixed(2)} (${knownWords.length}/${words.length})`);

            // 检查是否包含马来语地址关键词
            const malayAddressKeywords = ['jalan', 'kampung', 'taman', 'bandar', 'sungai', 'bukit'];
            const hasMalayKeywords = malayAddressKeywords.some(keyword =>
                address.toLowerCase().includes(keyword)
            );

            if (hasMalayKeywords) {
                console.log(`✅ 包含马来语地址关键词，判定为马来语地址`);
                translationStats.englishDetected++;
                return true;
            }

            // 如果已知单词比例超过60%，认为是英文/马来语地址
            if (knownWordRatio >= 0.6) {
                console.log(`✅ 已知单词比例${(knownWordRatio*100).toFixed(1)}% >= 60%，判定为英文/马来语地址`);
                translationStats.englishDetected++;
                return true;
            }

            // 特殊情况：如果地址很短且包含数字和英文，可能是门牌号
            if (words.length <= 3 && /\d/.test(address) && knownWordRatio >= 0.5) {
                console.log(`✅ 短地址包含数字和英文，判定为英文/马来语地址`);
                translationStats.englishDetected++;
                return true;
            }

            console.log(`❌ 判定为需要翻译的地址`);
            return false;
        }

        // 保持向后兼容性的别名
        const isEnglishAddress = isEnglishMalayAddress;

        /**
         * @function detectHotelInAddress - 检测地址中是否包含酒店名称
         * @param {string} address - 地址字符串
         * @returns {Object} 检测结果 {hasHotel: boolean, hotelName: string, hotelKeywords: string[]}
         */
        function detectHotelInAddress(address) {
            if (!address || address.trim() === '') {
                return { hasHotel: false, hotelName: '', hotelKeywords: [] };
            }

            console.log(`🏨 检测地址中的酒店名称: "${address}"`);

            // 酒店关键词列表（中英文）
            const hotelKeywords = [
                '酒店', '宾馆', '饭店', '旅馆', '客栈', '度假村', '套房', '公寓酒店', '服务式公寓',
                'hotel', 'resort', 'suites', 'inn', 'lodge', 'motel', 'hostel', 'guesthouse',
                'serviced apartment', 'residence', 'villa', 'boutique hotel', 'luxury hotel'
            ];

            // 知名酒店品牌和特殊名称模式
            const hotelBrandPatterns = [
                '香格里拉', '莎莉亚', '丽思卡尔顿', '万豪', '希尔顿', '凯悦', '洲际', '四季',
                '文华东方', '半岛', '瑞吉', '威斯汀', '喜来登', '万丽', '艾美', '雅高',
                'shangri-la', 'rasa ria', 'ritz-carlton', 'marriott', 'hilton', 'hyatt',
                'intercontinental', 'four seasons', 'mandarin oriental', 'peninsula',
                'st. regis', 'westin', 'sheraton', 'renaissance', 'le meridien', 'accor'
            ];

            // 检查是否包含酒店关键词
            const foundKeywords = hotelKeywords.filter(keyword =>
                address.toLowerCase().includes(keyword.toLowerCase())
            );

            // 检查是否包含知名酒店品牌模式
            const foundBrandPatterns = hotelBrandPatterns.filter(pattern =>
                address.toLowerCase().includes(pattern.toLowerCase())
            );

            if (foundKeywords.length === 0 && foundBrandPatterns.length === 0) {
                console.log(`📝 未检测到酒店关键词或品牌模式`);
                return { hasHotel: false, hotelName: '', hotelKeywords: [] };
            }

            // 合并找到的关键词和品牌模式
            const allFoundPatterns = [...foundKeywords, ...foundBrandPatterns];

            console.log(`🏨 检测到酒店关键词: [${foundKeywords.join(', ')}]`);
            console.log(`🏨 检测到酒店品牌模式: [${foundBrandPatterns.join(', ')}]`);

            // 尝试提取酒店名称
            let hotelName = '';

            // 方法1: 优先处理品牌模式（通常是完整的酒店名称）
            if (foundBrandPatterns.length > 0) {
                // 对于品牌模式，通常整个地址就是酒店名称
                hotelName = address.trim();
                console.log(`🏨 使用品牌模式提取酒店名称: ${hotelName}`);
            }
            // 方法2: 查找包含酒店关键词的完整名称
            else {
                for (const keyword of foundKeywords) {
                    const regex = new RegExp(`([^,，。；;\\n]*${keyword}[^,，。；;\\n]*)`, 'i');
                    const match = address.match(regex);
                    if (match && match[1]) {
                        hotelName = match[1].trim();
                        break;
                    }
                }

                // 方法3: 如果没有找到，尝试更宽松的匹配
                if (!hotelName) {
                    for (const keyword of foundKeywords) {
                        const regex = new RegExp(`([^\\n]*${keyword})`, 'i');
                        const match = address.match(regex);
                        if (match && match[1]) {
                            hotelName = match[1].trim();
                            break;
                        }
                    }
                }
            }

            console.log(`🏨 提取的酒店名称: "${hotelName}"`);

            return {
                hasHotel: true,
                hotelName: hotelName || address,
                hotelKeywords: allFoundPatterns
            };
        }

        /**
         * @function translateHotelName - 专门翻译酒店名称的函数
         * @param {string} hotelName - 酒店名称
         * @param {string} originalOrder - 原始订单数据（可选，用于提取官方英文名）
         * @returns {Promise<string>} 翻译后的酒店名称
         */
        async function translateHotelName(hotelName, originalOrder = '') {
            if (!hotelName || hotelName.trim() === '') return hotelName;

            console.log(`🏨 开始专门翻译酒店名称: "${hotelName}"`);

            // Tier 1: 检查本地酒店映射数据库（优先级最高）
            const localMatch = fuzzyMatchLocal(hotelName);
            if (localMatch) {
                console.log(`✅ 本地酒店数据库匹配成功: ${hotelName} -> ${localMatch}`);
                return localMatch;
            }

            // Tier 2: 从原始订单数据中提取官方英文名
            if (originalOrder) {
                const officialEnglishName = extractOfficialHotelName(originalOrder, hotelName);
                if (officialEnglishName) {
                    console.log(`✅ 从订单数据提取官方英文名: ${hotelName} -> ${officialEnglishName}`);
                    // 缓存这个映射
                    translationCache.set(hotelName, officialEnglishName);
                    return officialEnglishName;
                }
            }

            // Tier 3: 使用Gemini API专门处理酒店名称（替代DeepSeek）
            try {
                console.log(`🔄 使用Gemini API翻译酒店名称: ${hotelName}`);
                apiCallCount++;
                translationStats.apiCalls++;

                const geminiResult = await queryHotelEnglishNameWithGemini(hotelName);
                if (geminiResult && geminiResult !== hotelName) {
                    // 验证酒店翻译结果
                    const validation = validateHotelTranslation(hotelName, geminiResult);
                    if (validation.isValid) {
                        console.log(`✅ Gemini酒店名称翻译验证通过: ${hotelName} -> ${geminiResult}`);
                        translationCache.set(hotelName, geminiResult);
                        return geminiResult;
                    } else {
                        console.warn(`⚠️ Gemini酒店名称翻译验证失败: ${validation.reason}`);
                    }
                }
            } catch (error) {
                console.warn(`❌ Gemini酒店名称翻译失败: ${error.message}`);
            }

            console.log(`📝 所有酒店翻译方法均失败，返回原名称: ${hotelName}`);
            return hotelName;
        }

        /**
         * @function extractOfficialHotelName - 从原始订单数据中提取官方英文酒店名
         * @param {string} orderData - 原始订单数据
         * @param {string} chineseHotelName - 中文酒店名称
         * @returns {string|null} 官方英文酒店名称
         */
        function extractOfficialHotelName(orderData, chineseHotelName) {
            if (!orderData || !chineseHotelName) return null;

            console.log(`🔍 从订单数据中提取官方英文酒店名`);

            // 查找"酒店（英文）："模式
            const englishHotelPattern = /酒店[（(]英文[）)][:：]\s*([^\n\r,，。；;]+)/i;
            const match = orderData.match(englishHotelPattern);

            if (match && match[1]) {
                const officialName = match[1].trim();
                // 检查提取的名称是否真的是英文（不包含中文字符）
                if (!/[\u4e00-\u9fa5]/.test(officialName)) {
                    console.log(`✅ 找到官方英文酒店名: ${officialName}`);
                    return officialName;
                } else {
                    console.log(`⚠️ 提取的酒店名称包含中文字符，不是英文名称: ${officialName}`);
                }
            }

            // 查找其他可能的英文酒店名模式
            const alternativePatterns = [
                /hotel[：:]\s*([^\n\r,，。；;]+)/i,
                /english\s*name[：:]\s*([^\n\r,，。；;]+)/i,
                /official\s*name[：:]\s*([^\n\r,，。；;]+)/i
            ];

            for (const pattern of alternativePatterns) {
                const altMatch = orderData.match(pattern);
                if (altMatch && altMatch[1]) {
                    const officialName = altMatch[1].trim();
                    // 检查提取的名称是否真的是英文（不包含中文字符）
                    if (!/[\u4e00-\u9fa5]/.test(officialName)) {
                        console.log(`✅ 找到备选英文酒店名: ${officialName}`);
                        return officialName;
                    } else {
                        console.log(`⚠️ 备选酒店名称包含中文字符，不是英文名称: ${officialName}`);
                    }
                }
            }

            console.log(`📝 未在订单数据中找到官方英文酒店名`);
            return null;
        }

        /**
         * @function validateHotelTranslation - 验证酒店名称翻译结果
         * @param {string} original - 原始酒店名称
         * @param {string} translated - 翻译结果
         * @returns {Object} 验证结果 {isValid: boolean, reason: string}
         */
        function validateHotelTranslation(original, translated) {
            // 基本检查
            if (!translated || translated.trim() === '') {
                return { isValid: false, reason: '翻译结果为空' };
            }

            // 检查是否包含错误提示
            const errorIndicators = ['翻译', '无法', '不能', '错误', 'error', 'unable', 'cannot', 'failed'];
            if (errorIndicators.some(indicator => translated.toLowerCase().includes(indicator))) {
                return { isValid: false, reason: '翻译结果包含错误提示' };
            }

            // 检查长度合理性
            if (translated.length > original.length * 4) {
                return { isValid: false, reason: '翻译结果过长，可能不准确' };
            }

            // 检查是否是明显的字面翻译（特殊情况）
            const literalTranslationPatterns = [
                /^lane\s+hotel$/i,  // "莱恩酒店" -> "Lane Hotel"
                /^lai\s+en\s+hotel$/i,  // "莱恩酒店" -> "Lai En Hotel"
                /^ryan\s+hotel$/i,  // "莱恩酒店" -> "Ryan Hotel"
                /^leon\s+hotel$/i   // "莱恩酒店" -> "Leon Hotel"
            ];

            if (literalTranslationPatterns.some(pattern => pattern.test(translated))) {
                return { isValid: false, reason: '检测到字面翻译，可能不是官方名称' };
            }

            // 检查是否包含酒店关键词
            const hotelKeywords = ['hotel', 'resort', 'suites', 'inn', 'lodge'];
            const hasHotelKeyword = hotelKeywords.some(keyword =>
                translated.toLowerCase().includes(keyword)
            );

            if (!hasHotelKeyword && original.includes('酒店')) {
                return { isValid: false, reason: '翻译结果缺少酒店关键词' };
            }

            return { isValid: true, reason: '验证通过' };
        }

        /**
         * @function queryHotelEnglishNameWithGemini - 使用Gemini API查询酒店英文名称
         * @param {string} hotelName - 中文酒店名称
         * @returns {Promise<string>} 英文酒店名称
         */
        async function queryHotelEnglishNameWithGemini(hotelName) {
            const prompt = `请提供以下中文酒店名称的官方英文名称。请注意：

1. 优先返回酒店的官方英文名称，而不是字面翻译
2. 如果是知名酒店品牌，请使用标准的英文名称
3. 避免字面翻译，例如"莱恩酒店"的官方英文名是"Sleeping Lion Hotel"而不是"Lane Hotel"
4. 如果不确定官方名称，请基于酒店的含义和背景提供合理的英文名称
5. 保持酒店名称的专业性和准确性

中文酒店名称：${hotelName}

请只返回英文酒店名称，不要包含其他解释。`;

            try {
                const response = await fetch(
                    `${window.API_CONFIG.gemini.baseUrl}/${window.API_CONFIG.gemini.model}:generateContent?key=${window.API_CONFIG.gemini.apiKey}`,
                    {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: prompt
                                }]
                            }]
                        })
                    }
                );

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log("Gemini API酒店名称翻译返回结果:", data);

                if (data && data.candidates && data.candidates.length > 0 &&
                    data.candidates[0].content && data.candidates[0].content.parts &&
                    data.candidates[0].content.parts.length > 0) {

                    let result = data.candidates[0].content.parts[0].text.trim();
                    console.log('原始翻译结果:', result);

                    // 清理结果，移除可能的引号和多余空格
                    result = result.replace(/^["']|["']$/g, '').trim();

                    // 提取实际的酒店名称（处理Gemini的详细回复）
                    const boldMatch = result.match(/\*\*([^*]+)\*\*/);
                    if (boldMatch && boldMatch[1]) {
                        result = boldMatch[1].trim();
                    } else {
                        // 如果没有加粗文本，尝试提取第一行或最简洁的部分
                        const lines = result.split('\n').filter(line => line.trim());
                        if (lines.length > 0) {
                            // 查找不包含解释性词汇的行
                            const cleanLine = lines.find(line =>
                                !line.includes('根据') &&
                                !line.includes('可以翻译') &&
                                !line.includes('这是') &&
                                !line.includes('如果') &&
                                line.trim().length > 0
                            );
                            if (cleanLine) {
                                result = cleanLine.replace(/^[*\-•]\s*/, '').trim();
                            } else {
                                result = lines[0].trim();
                            }
                        }
                    }

                    // 最终清理
                    result = result.replace(/^[*\-•]\s*/, '').trim();

                    console.log(`🏨 Gemini酒店名称API响应: ${result}`);
                    return result || hotelName;
                } else {
                    throw new Error('无法从Gemini API响应中提取翻译结果');
                }

            } catch (error) {
                console.error('Gemini酒店名称API查询失败:', error);
                throw error;
            }
        }

        /**
         * @function queryAddressEnglishNameWithGemini - 使用Gemini API翻译地址名称
         * @param {string} chineseAddress - 中文地址名称
         * @returns {Promise<string>} 翻译后的英文地址名称
         * @description 专门用于地址翻译的Gemini API调用函数，根据用户偏好使用Gemini而非DeepSeek
         */
        async function queryAddressEnglishNameWithGemini(chineseAddress) {
            if (!chineseAddress || chineseAddress.trim().length === 0) return chineseAddress;

            const prompt = `请将以下中文地址翻译成英文。请注意：

1. 保持地址的准确性和可识别性
2. 保留已有的英文和马来语单词不变
3. 对于知名地标、酒店、机场等，使用官方英文名称
4. 保持地址格式的合理性
5. 只返回翻译后的英文地址，不要包含其他解释
6. 如果地址包含混合语言，只翻译中文部分

中文地址：${chineseAddress}

请只返回英文地址，不要包含其他解释。`;

            try {
                console.log(`开始使用Gemini API翻译地址: ${chineseAddress}`);

                const response = await fetch(
                    `${window.API_CONFIG.gemini.baseUrl}/${window.API_CONFIG.gemini.model}:generateContent?key=${window.API_CONFIG.gemini.apiKey}`,
                    {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: prompt
                                }]
                            }]
                        })
                    }
                );

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log("Gemini API地址翻译返回结果:", data);

                if (data && data.candidates && data.candidates.length > 0 &&
                    data.candidates[0].content && data.candidates[0].content.parts &&
                    data.candidates[0].content.parts.length > 0) {

                    let result = data.candidates[0].content.parts[0].text.trim();
                    console.log('原始翻译结果:', result);

                    // 清理结果，移除可能的引号和多余空格
                    result = result.replace(/^["']|["']$/g, '').trim();

                    // 提取实际的翻译结果（处理Gemini的详细回复）
                    // 查找加粗的文本（**text**）或者最后一行的简洁翻译
                    const boldMatch = result.match(/\*\*([^*]+)\*\*/);
                    if (boldMatch && boldMatch[1]) {
                        result = boldMatch[1].trim();
                    } else {
                        // 如果没有加粗文本，尝试提取第一行或最简洁的部分
                        const lines = result.split('\n').filter(line => line.trim());
                        if (lines.length > 0) {
                            // 查找不包含解释性词汇的行
                            const cleanLine = lines.find(line =>
                                !line.includes('根据') &&
                                !line.includes('可以翻译') &&
                                !line.includes('这是') &&
                                !line.includes('如果') &&
                                line.trim().length > 0
                            );
                            if (cleanLine) {
                                result = cleanLine.replace(/^[*\-•]\s*/, '').trim();
                            } else {
                                result = lines[0].trim();
                            }
                        }
                    }

                    // 最终清理
                    result = result.replace(/^[*\-•]\s*/, '').trim();

                    console.log('处理后的翻译结果:', result);
                    console.log(`Gemini API翻译成功: ${chineseAddress} -> ${result}`);

                    return result;
                } else if (data && data.choices && data.choices.length > 0 &&
                          data.choices[0].message && data.choices[0].message.content) {

                    let result = data.choices[0].message.content.trim();
                    console.log('原始翻译结果:', result);

                    // 清理结果，移除可能的引号和多余空格
                    result = result.replace(/^["']|["']$/g, '').trim();

                    // 移除可能的解释性文本
                    const lines = result.split('\n');
                    result = lines[0].trim();

                    console.log('处理后的翻译结果:', result);
                    console.log(`Gemini API翻译成功: ${chineseAddress} -> ${result}`);

                    return result;
                } else {
                    throw new Error('无法从Gemini API响应中提取翻译结果');
                }
            } catch (error) {
                console.error("Gemini API地址翻译失败:", error);
                throw error;
            }
        }

        /**
         * @function processAirportKeywords - 处理机场关键词映射
         * @param {string} address - 地址字符串
         * @returns {string} 处理后的地址
         */
        function processAirportKeywords(address) {
            // 机场关键词映射
            const airportKeywords = {
                '樟宜机场': 'Changi Airport',
                '吉隆坡机场': 'Kuala Lumpur International Airport',
                '吉隆坡国际机场1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场2': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡国际机场t1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场t2': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡国际机场T1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡国际机场T2': 'Kuala Lumpur International Airport Terminal 2',
                '吉隆坡T1': 'Kuala Lumpur International Airport Terminal 1',
                '吉隆坡T2': 'Kuala Lumpur International Airport Terminal 2',
                'KLIA': 'Kuala Lumpur International Airport',
                'KLIA1': 'Kuala Lumpur International Airport Terminal 1',
                'KLIA2': 'Kuala Lumpur International Airport Terminal 2',
                'T1-吉隆坡': 'T1-Kuala Lumpur',
                'T2-吉隆坡': 'T2-Kuala Lumpur',
                '斗湖机场': 'Tawau Airport',
                '亚庇机场': 'Kota Kinabalu International Airport',
                '槟城机场': 'Penang International Airport',
                '新山机场': 'Senai International Airport',
                '沙巴机场': 'Kota Kinabalu International Airport',
                '仙本那机场': 'Semporna Airport',
                '新加坡机场': 'Changi International Airport'
            };

            // 检查特定机场关键词
            for (const [keyword, translation] of Object.entries(airportKeywords)) {
                if (address.includes(keyword)) {
                    translationStats.airportKeywords++;
                    return address.replace(keyword, translation);
                }
            }

            return address;
        }

        /**
         * @function getFromCache - 从缓存中获取翻译结果
         * @param {string} address - 地址字符串
         * @returns {string|null} 缓存的翻译结果
         */
        function getFromCache(address) {
            // 检查运行时缓存
            if (translationCache.has(address)) {
                return translationCache.get(address);
            }

            // 检查常见地址模式
            if (commonAddressPatterns.has(address)) {
                const result = commonAddressPatterns.get(address);
                translationCache.set(address, result);
                return result;
            }

            // 检查本地翻译映射
            const localMatch = fuzzyMatchLocal(address);
            if (localMatch) {
                translationCache.set(address, localMatch);
                return localMatch;
            }

            return null;
        }

        /**
         * @function cacheTranslation - 缓存翻译结果
         * @param {string} original - 原始文本
         * @param {string} translated - 翻译结果
         */
        function cacheTranslation(original, translated) {
            translationCache.set(original, translated);
            // 同时添加到常见地址模式中（如果是完整地址）
            if (original.length > 10 && !original.includes(' ')) {
                commonAddressPatterns.set(original, translated);
            }
        }

        /**
         * @function batchTranslateAddress - 批量翻译整个地址（使用Gemini API）
         * @param {string} address - 完整地址
         * @returns {Promise<string>} 翻译后的地址
         * @description 根据用户偏好，地址翻译使用Gemini API而非DeepSeek API
         */
        async function batchTranslateAddress(address) {
            try {
                console.log(`🔄 尝试批量翻译整个地址: ${address}`);
                apiCallCount++;
                translationStats.batchTranslations++;

                const result = await queryAddressEnglishNameWithGemini(address);

                // 验证翻译结果的质量
                if (result && result !== address) {
                    const validationResult = validateTranslationResult(address, result);
                    if (validationResult.isValid) {
                        console.log(`✅ 批量翻译验证通过: ${address} -> ${result}`);
                        return result;
                    } else {
                        console.warn(`⚠️ 批量翻译验证失败: ${validationResult.reason}`);
                        console.warn(`原地址: ${address}`);
                        console.warn(`翻译结果: ${result}`);
                    }
                }
            } catch (error) {
                console.warn(`❌ 批量翻译失败: ${error.message}`);
            }

            return address;
        }

        /**
         * @function validateTranslationResult - 验证翻译结果质量
         * @param {string} original - 原始地址
         * @param {string} translated - 翻译结果
         * @returns {Object} 验证结果 {isValid: boolean, reason: string}
         */
        function validateTranslationResult(original, translated) {
            // 基本检查
            if (!translated || translated.trim() === '') {
                return { isValid: false, reason: '翻译结果为空' };
            }

            // 检查是否包含错误提示
            const errorIndicators = ['翻译', '无法', '不能', '错误', 'error', 'unable', 'cannot', 'failed'];
            if (errorIndicators.some(indicator => translated.toLowerCase().includes(indicator))) {
                return { isValid: false, reason: '翻译结果包含错误提示' };
            }

            // 检查长度合理性（翻译结果不应该比原文长太多）
            // 对于中文到英文的翻译，允许更大的长度比例，特别是对于酒店、地名和机场名称
            let maxLengthRatio = 4; // 默认比例

            // 酒店/度假村名称通常有更长的英文全称
            if (original.includes('香格里拉') || original.includes('莎莉亚') ||
                translated.toLowerCase().includes('resort') || translated.toLowerCase().includes('hotel') ||
                translated.toLowerCase().includes('shangri-la')) {
                maxLengthRatio = 8;
            }
            // 机场和国际地标
            else if (original.includes('机场') || original.includes('国际') ||
                     translated.toLowerCase().includes('airport') || translated.toLowerCase().includes('international')) {
                maxLengthRatio = 6;
            }
            // 包含地名的复合地址
            else if (original.length <= 10 && translated.split(' ').length >= 3) {
                maxLengthRatio = 7;
            }

            if (translated.length > original.length * maxLengthRatio) {
                return { isValid: false, reason: '翻译结果过长，可能不准确' };
            }

            // 检查是否完全不相关（保留一些原始关键词）
            const originalWords = original.toLowerCase().split(/[\s,\-\/\(\)\[\]]+/).filter(Boolean);
            const translatedWords = translated.toLowerCase().split(/[\s,\-\/\(\)\[\]]+/).filter(Boolean);

            // 检查是否保留了一些原始的英文/马来语单词
            const preservedWords = originalWords.filter(word =>
                englishMalayWords.has(word) && translatedWords.includes(word)
            );

            // 如果原地址包含英文/马来语单词，翻译结果应该保留一些
            const originalEnglishMalayWords = originalWords.filter(word => englishMalayWords.has(word));
            if (originalEnglishMalayWords.length > 0 && preservedWords.length === 0) {
                return { isValid: false, reason: '翻译结果未保留原有的英文/马来语单词' };
            }

            // 检查地理相关性（简单检查）
            const geographicKeywords = [
                'kuala', 'lumpur', 'singapore', 'penang', 'johor', 'sabah', 'sarawak',
                'kl', 'sg', 'jb', 'kota', 'kinabalu', 'changi', 'airport', 'terminal'
            ];

            const originalHasGeo = geographicKeywords.some(keyword =>
                original.toLowerCase().includes(keyword)
            );
            const translatedHasGeo = geographicKeywords.some(keyword =>
                translated.toLowerCase().includes(keyword)
            );

            // 如果原地址有地理关键词，翻译结果也应该有相关的地理信息
            if (originalHasGeo && !translatedHasGeo) {
                return { isValid: false, reason: '翻译结果缺少地理相关信息' };
            }

            return { isValid: true, reason: '验证通过' };
        }

        /**
         * @function smartSegmentTranslation - 智能分段翻译（增强混合语言处理）
         * @param {string} address - 地址字符串
         * @returns {Promise<string>} 翻译后的地址
         */
        async function smartSegmentTranslation(address) {
            console.log(`🧠 开始智能分段翻译: ${address}`);

            // 保留原始分隔符的分割方式
            const parts = address.split(/(\s|,|\-|\/|\(|\)|\[|\])+/);
            const processedParts = [];
            let hasTranslation = false;
            let apiCallsInThisFunction = 0;

            for (let i = 0; i < parts.length; i++) {
                const part = parts[i];

                // 保留分隔符
                if (/^(\s|,|\-|\/|\(|\)|\[|\])+$/.test(part)) {
                    processedParts.push(part);
                    continue;
                }

                // 跳过空字符串
                if (!part || part.trim() === '') {
                    processedParts.push(part);
                    continue;
                }

                const trimmedPart = part.trim();

                // 跳过数字
                if (/^\d+$/.test(trimmedPart)) {
                    console.log(`📝 跳过数字: ${trimmedPart}`);
                    processedParts.push(part);
                    continue;
                }

                // 跳过已知的英文/马来语单词
                if (englishMalayWords.has(trimmedPart.toLowerCase())) {
                    console.log(`📝 跳过已知英文/马来语单词: ${trimmedPart}`);
                    processedParts.push(part);
                    continue;
                }

                // 跳过纯英文段落（不含中文字符）
                if (!/[\u4e00-\u9fa5]/.test(trimmedPart)) {
                    console.log(`📝 跳过纯英文段落: ${trimmedPart}`);
                    processedParts.push(part);
                    continue;
                }

                // 限制API调用次数
                if (apiCallsInThisFunction >= 2) {
                    console.log(`⚠️ 达到API调用限制(${apiCallsInThisFunction}/2)，跳过剩余段落翻译`);
                    processedParts.push(part);
                    continue;
                }

                // 翻译包含中文的段落
                try {
                    console.log(`🔄 尝试翻译中文段落: ${trimmedPart}`);
                    const translatedSegment = await translateWithCache(trimmedPart);

                    if (translatedSegment && translatedSegment !== trimmedPart) {
                        // 验证翻译结果
                        const validation = validateTranslationResult(trimmedPart, translatedSegment);
                        if (validation.isValid) {
                            console.log(`✅ 段落翻译成功: ${trimmedPart} -> ${translatedSegment}`);
                            processedParts.push(part.replace(trimmedPart, translatedSegment));
                            hasTranslation = true;
                            apiCallsInThisFunction++;
                        } else {
                            console.warn(`⚠️ 段落翻译验证失败: ${validation.reason}`);
                            processedParts.push(part);
                        }
                    } else {
                        console.log(`📝 段落翻译无变化: ${trimmedPart}`);
                        processedParts.push(part);
                    }
                } catch (error) {
                    console.warn(`❌ 段落翻译失败: ${trimmedPart}`, error);
                    processedParts.push(part);
                }
            }

            if (hasTranslation) {
                translationStats.smartSegmentations++;
                const result = processedParts.join('');
                console.log(`✅ 智能分段翻译完成: ${address} -> ${result}`);
                return result;
            }

            console.log(`📝 智能分段翻译无变化，返回原地址`);
            return address;
        }

        /**
         * @function processHotelAddress - 专门处理包含酒店的地址
         * @param {string} address - 完整地址
         * @param {Object} hotelDetection - 酒店检测结果
         * @returns {Promise<string>} 处理后的地址
         */
        async function processHotelAddress(address, hotelDetection) {
            console.log(`🏨 开始处理酒店地址: "${address}"`);
            console.log(`🏨 酒店信息: 名称="${hotelDetection.hotelName}", 关键词=[${hotelDetection.hotelKeywords.join(', ')}]`);

            try {
                // 获取原始订单数据（如果可用）
                const rawOrderData = document.getElementById('raw-order')?.value || '';

                // 翻译酒店名称
                const translatedHotelName = await translateHotelName(hotelDetection.hotelName, rawOrderData);

                if (translatedHotelName !== hotelDetection.hotelName) {
                    // 替换地址中的酒店名称
                    const updatedAddress = address.replace(hotelDetection.hotelName, translatedHotelName);
                    console.log(`✅ 酒店名称翻译成功: ${hotelDetection.hotelName} -> ${translatedHotelName}`);
                    console.log(`✅ 更新后的地址: ${address} -> ${updatedAddress}`);

                    // 缓存酒店翻译结果
                    cacheTranslation(hotelDetection.hotelName, translatedHotelName);
                    cacheTranslation(address, updatedAddress);

                    return updatedAddress;
                } else {
                    console.log(`📝 酒店名称无需翻译或翻译失败，保持原样`);
                }

                // 如果酒店名称翻译失败，尝试翻译地址的其他部分
                const nonHotelParts = address.replace(hotelDetection.hotelName, '').trim();
                if (nonHotelParts) {
                    console.log(`🔄 尝试翻译地址的非酒店部分: "${nonHotelParts}"`);
                    const translatedNonHotelParts = await smartSegmentTranslation(nonHotelParts);

                    if (translatedNonHotelParts !== nonHotelParts) {
                        const combinedResult = `${hotelDetection.hotelName} ${translatedNonHotelParts}`.trim();
                        console.log(`✅ 部分翻译成功: ${address} -> ${combinedResult}`);
                        return combinedResult;
                    }
                }

            } catch (error) {
                console.warn(`❌ 酒店地址处理失败: ${error.message}`);
            }

            return address;
        }

        /**
         * @function logProcessingTime - 记录处理时间
         * @param {number} startTime - 开始时间
         * @param {string} method - 处理方法
         */
        function logProcessingTime(startTime, method) {
            const endTime = performance.now();
            const processingTime = endTime - startTime;
            totalProcessingTime += processingTime;

            console.log(`⏱️ ${method} 处理时间: ${processingTime.toFixed(2)}ms`);

            // 每10次处理后输出统计信息
            if ((translationStats.cacheHits + translationStats.apiCalls) % 10 === 0) {
                logTranslationStats();
            }
        }

        /**
         * @function logTranslationStats - 输出翻译统计信息
         */
        function logTranslationStats() {
            console.group('📊 地址翻译性能统计');
            console.log(`API调用次数: ${apiCallCount}`);
            console.log(`总处理时间: ${totalProcessingTime.toFixed(2)}ms`);
            console.log(`平均处理时间: ${(totalProcessingTime / (translationStats.cacheHits + translationStats.apiCalls)).toFixed(2)}ms`);
            console.log(`缓存命中: ${translationStats.cacheHits}`);
            console.log(`API调用: ${translationStats.apiCalls}`);
            console.log(`英文检测: ${translationStats.englishDetected}`);
            console.log(`机场关键词: ${translationStats.airportKeywords}`);
            console.log(`批量翻译: ${translationStats.batchTranslations}`);
            console.log(`智能分段: ${translationStats.smartSegmentations}`);
            console.log(`缓存命中率: ${((translationStats.cacheHits / (translationStats.cacheHits + translationStats.apiCalls)) * 100).toFixed(1)}%`);
            console.groupEnd();
        }

        // DOM元素引用
        const rawOrderTextarea = document.getElementById('raw-order');
        const convertBtn = document.getElementById('convert-btn');
        const resetBtn = document.getElementById('reset-btn');
        const editBtn = document.getElementById('edit-btn');
        const saveBtn = document.getElementById('save-btn');
        const copyOutputBtn = document.getElementById('copy-output');
        const formInputs = document.querySelectorAll('#order-form input, #order-form select, #order-form textarea');
        const multiOrdersContainer = document.getElementById('multi-orders-container');
        const ordersList = document.getElementById('orders-list');
        const notification = document.getElementById('notification');
        const languageSelector = document.getElementById('language-selector');

        /**
         * @function convertOrder - 重构后的订单转换主函数
         * @description 支持DeepSeek API智能解析的异步订单转换函数
         */
        async function convertOrder() {
            // 获取原始订单文本
            const rawOrderText = rawOrderTextarea.value.trim();

            if (!rawOrderText) {
                const currentLang = languageSelector.value;
                showNotification(i18n[currentLang]['please-enter-data']);
                return;
            }

            // 显示加载动画
            showApiLoader();

            try {
                // 检查是否有多个订单
                const orderRegex = /订单编号：\d{19}/g;
                const orderMatches = [...rawOrderText.matchAll(orderRegex)];

                if (orderMatches.length > 1) {
                    // 多订单处理
                    await processMultipleOrders(rawOrderText);
                } else {
                    // 单订单处理
                    await processSingleOrder(rawOrderText);
                }

                // 显示成功通知
                const currentLang = languageSelector.value;
                showNotification('订单解析完成！使用了DeepSeek AI智能解析和Gemini AI地址翻译技术。');

            } catch (error) {
                console.error('订单转换过程中发生错误:', error);
                showNotification('订单解析失败，请检查订单格式或稍后重试。');
            } finally {
                // 隐藏加载动画
                hideApiLoader();
            }
        }

        // 处理单个订单
        async function processSingleOrder(rawOrderText) {
            // 显示标准订单表单，隐藏多订单容器
            document.querySelector('.grid').classList.remove('hidden');
            multiOrdersContainer.classList.add('hidden');

            // 提取订单数据（现在是异步的）
            const orderData = await extractOrderData(rawOrderText);

            // 处理pickup和dropoff地址翻译
            if (orderData['pickup-address']) {
                orderData['pickup-address'] = await processAddress(orderData['pickup-address']);
            }
            if (orderData['dropoff-address']) {
                orderData['dropoff-address'] = await processAddress(orderData['dropoff-address']);
            }

            // 填充表单
            await fillOrderForm(orderData);
        }

        // 处理多个订单
        async function processMultipleOrders(rawOrderText) {
            // 隐藏标准订单表单，显示多订单容器
            document.querySelector('.grid').classList.add('hidden');
            multiOrdersContainer.classList.remove('hidden');

            // 清空订单列表
            ordersList.innerHTML = '';

            // 分割多个订单
            const orders = splitOrders(rawOrderText);

            // 处理每个订单
            for (let i = 0; i < orders.length; i++) {
                const orderText = orders[i];
                const orderData = await extractOrderData(orderText); // 现在是异步的

                // 处理地址翻译
                if (orderData['pickup-address']) {
                    orderData['pickup-address'] = await processAddress(orderData['pickup-address']);
                }
                if (orderData['dropoff-address']) {
                    orderData['dropoff-address'] = await processAddress(orderData['dropoff-address']);
                }

                // 标记地址已翻译
                orderData['pickup-address-translated'] = true;
                orderData['dropoff-address-translated'] = true;

                // 确保使用vehicle-type作为主要车型字段
                if (orderData['vehicle-type'] && !orderData['car-type']) {
                    orderData['car-type'] = orderData['vehicle-type'];
                } else if (orderData['car-type'] && !orderData['vehicle-type']) {
                    orderData['vehicle-type'] = orderData['car-type'];
                }

                // 创建并添加订单元素
                const orderElement = createOrderElement(orderData, orderText, i + 1);
                ordersList.appendChild(orderElement);
            }
        }

        // 分割多个订单
        function splitOrders(rawOrderText) {
            // 使用"订单编号："作为分割点
            const orderRegex = /(订单编号：\d{19}[\s\S]*?)(?=订单编号：\d{19}|$)/g;
            const orders = [];
            let match;
            
            while ((match = orderRegex.exec(rawOrderText)) !== null) {
                orders.push(match[1].trim());
            }
            
            return orders;
        }

        /**
         * @function extractOrderData - 重构后的订单数据提取函数
         * @param {string} orderText - 原始订单文本
         * @returns {Promise<Object>} 提取的标准化订单数据
         * @description 首先尝试DeepSeek API智能解析，失败时回退到正则表达式解析
         */
        async function extractOrderData(orderText) {
            console.log('开始提取订单数据:', orderText);

            try {
                // 首先尝试使用DeepSeek API进行智能解析
                console.log('尝试使用DeepSeek API进行智能订单解析...');
                const aiParsedData = await parseOrderWithDeepSeek(orderText);

                if (aiParsedData && Object.keys(aiParsedData).length > 0) {
                    console.log('DeepSeek API解析成功，返回AI解析结果');
                    // 保存原始文本用于电话号码提取
                    aiParsedData.originalText = orderText;
                    // 应用增强功能
                    return validateAndCleanOrderData(aiParsedData, aiParsedData);
                }
            } catch (error) {
                console.warn('DeepSeek API解析失败，回退到正则表达式解析:', error);
            }

            // 回退到正则表达式解析
            console.log('使用正则表达式进行订单解析...');
            const regexParsedData = extractOrderDataWithRegex(orderText);

            // 保存原始文本用于电话号码提取
            regexParsedData.originalText = orderText;

            // 字段完整性验证和数据清洗
            const finalData = validateAndCleanOrderData(regexParsedData, regexParsedData);

            console.log('最终订单数据:', finalData);
            return finalData;
        }

        /**
         * @function extractOrderDataLegacy - 保留原有的正则表达式解析逻辑
         * @param {string} orderText - 原始订单文本
         * @returns {Object} 提取的订单数据
         * @description 保留原有复杂的正则表达式解析逻辑作为备用方案
         */
        function extractOrderDataLegacy(orderText) {
            const orderData = {
                ota: 'Jing Ge',
                language: 'Chinese',
                driver: 1,
                category: '',
                subcategory: ''
            };

            // 根据规则映射提取字段
            for (const mapping of jingGeRule.fieldMappings) {
                try {
                    const regex = new RegExp(mapping.pattern, 'i');
                    const match = orderText.match(regex);

                    if (match && match[1]) {
                        const fieldId = mapping.field;
                        orderData[fieldId] = match[1].trim();
                    }
                } catch (e) {
                    console.error(`提取字段 ${mapping.field} 时出错:`, e);
                }
            }
            
            // 车型识别逻辑（档次+座位模式）
            const lowerOrderText = orderText.toLowerCase();
            console.log('开始车型识别，订单文本:', lowerOrderText);
            
            // 1. 识别车辆档次
            const vehicleClass = 
                lowerOrderText.includes('豪华') ? '豪华' :
                lowerOrderText.includes('商务') ? '商务' :
                lowerOrderText.includes('经济') || lowerOrderText.includes('舒适') ? '经济' : '';
            
            // 2. 识别座位数
            const seatPatterns = [
                { pattern: /(?:五座|5座|5人座|V座|舒适5座)/, seats: '五座' },
                { pattern: /(?:七座|7座|7人座|VII座|舒适7座)/, seats: '七座' },
                { pattern: /(?:九座|9座|9人座|IX座)/, seats: '九座' },
                { pattern: /(?:十五座|15座|18座|XV座|14座中巴)/, seats: '十五座' }
            ];
            
            let seatType = '';
            for (const {pattern, seats} of seatPatterns) {
                if (pattern.test(lowerOrderText)) {
                    seatType = seats;
                    break;
                }
            }
            
            // 3. 组合判断
            if (seatType === '十五座' || lowerOrderText.includes('中巴')) {
                // 中巴类特殊处理
                orderData['vehicle-type'] = 'Van';
                console.log('中巴车型识别: Van');
            }
            else if (vehicleClass && seatType) {
                const key = `${vehicleClass}${seatType}`;
                const standardMapping = {
                    '经济五座': 'sedan',
                    '经济七座': 'SUV',
                    '商务七座': 'Serena',
                    '豪华七座': 'Alphard',
                    '商务九座': 'Starex'
                };
                
                if (standardMapping[key]) {
                    console.log('识别到车型:', key, '=>', standardMapping[key]);
                    orderData['vehicle-type'] = standardMapping[key];
                } else {
                    console.log('未配置的车型组合:', key);
                }
            } else {
                console.log('车型识别失败:', 
                    `档次=${vehicleClass || '未识别'}, ` +
                    `座位=${seatType || '未识别'}`);
            }
            
            // 将reference映射到ota-reference
            if (orderData.reference) {
                orderData['ota-reference'] = orderData.reference;
            }
            
            // 将merchant-received映射到price，并根据区域计算价格
            if (orderData['merchant-received']) {
                const price = parseFloat(orderData['merchant-received']);
                if (!isNaN(price)) {
                    // 基础费率
                    const baseRate = 0.84;
                    // 分别识别两国关键词
                    const hasMalaysia = /马来西亚[\-－]?吉隆坡|malaysia|kl|penang|johor|sabah|吉隆坡|槟城|柔佛|沙巴/i.test(orderText);
                    const hasSingapore = /新加坡|singapore|sg|狮城/i.test(orderText);
                    const regionRate = hasSingapore ? 0.18 : hasMalaysia ? 0.61 : 0.61;
                    orderData.price = (price * baseRate * regionRate).toFixed(2);
                }
            }
            
            // 处理航班号：优先使用主要提取规则，如果失败则使用备用规则
            if (orderData.flight) {
                orderData['flight-number'] = orderData.flight;
            } else if (orderData['flight-backup']) {
                orderData['flight-number'] = orderData['flight-backup'];
            } else {
                // 直接从原始文本中搜索可能的航班号格式
                const flightNumberPatterns = [
                    /\b([A-Z]{2}\d{1,4})\b/,      // 标准航班号：两个字母+数字（如CX123）
                    /\b([A-Z]{3}\d{1,4})\b/,      // 三字母代码+数字（如CPA123）
                    /\b(\d{1,2}[A-Z]{1,2}\d{1,4})\b/, // 数字+字母+数字（如3K536, 9W123）
                    /\b([A-Z]\d{3,4})\b/,         // 单字母+数字（如G5123）
                    /\b([A-Z]{2}\d{1,3}[A-Z])\b/  // 字母+数字+字母（如MU123A）
                ];
                
                for (const pattern of flightNumberPatterns) {
                    const match = orderText.match(pattern);
                    if (match && match[1]) {
                        orderData['flight-number'] = match[1];
                        break;
                    }
                }
            }
            
            // 清理无用的临时字段
            delete orderData.flight;
            delete orderData['flight-backup'];
            
            // 将passengers映射到passenger-number
            if (orderData.passengers) {
                orderData['passenger-number'] = orderData.passengers;
            }
            
            // 判断是接机还是送机
            determineOrderType(orderText, orderData);
            
            // 判断驾驶区域
            determineDrivingRegion(orderText, orderData);
            
            return orderData;
        }

        // 判断订单类型 (接机、送机或包车)
        function determineOrderType(orderText, orderData) {
            // 关键词检测
            const pickupKeywords = ['接机', '接送机', '接车', '机场接', '接送服务', 'airport pickup', 'pick up', 'pick-up', 'arrival', 'meet and greet'];
            const dropoffKeywords = ['送机', '去机场', '机场送', '送往机场', 'airport drop-off', 'drop off', 'departure', 'send to airport'];
            
            const lowerOrderText = orderText.toLowerCase();
            const hasPickupKeywords = pickupKeywords.some(keyword => 
                lowerOrderText.includes(keyword.toLowerCase())
            );
            const hasDropoffKeywords = dropoffKeywords.some(keyword => 
                lowerOrderText.includes(keyword.toLowerCase())
            );
            
            // 判断是否是机场相关订单
            const airportKeywords = ['机场', '航站', '航厦', '空港', 'airport', 'terminal'];
            const hasAirportKeywords = airportKeywords.some(keyword => 
                lowerOrderText.includes(keyword.toLowerCase())
            );
            
            if (hasAirportKeywords || hasPickupKeywords || hasDropoffKeywords) {
                orderData.category = 'airport';
                
                if (hasPickupKeywords && !hasDropoffKeywords) {
                    orderData.subcategory = 'pickup';
                } else if (hasDropoffKeywords && !hasPickupKeywords) {
                    orderData.subcategory = 'dropoff';
                } else {
                    // 如果两种关键词都有或都没有，根据地址判断
                    const pickupHasAirport = airportKeywords.some(keyword => 
                        (orderData['pickup-address'] || '').toLowerCase().includes(keyword.toLowerCase())
                    );
                    const dropoffHasAirport = airportKeywords.some(keyword => 
                        (orderData['dropoff-address'] || '').toLowerCase().includes(keyword.toLowerCase())
                    );
                    
                    if (pickupHasAirport) {
                        orderData.subcategory = 'pickup';
                    } else if (dropoffHasAirport) {
                        orderData.subcategory = 'dropoff';
                    } else {
                        orderData.subcategory = 'pickup'; // 默认为接机
                    }
                }
            }
            else {
                orderData.category = 'Charter 包车';
                orderData.subcategory = 'charter';
            }
        }

        // 判断驾驶区域
        function determineDrivingRegion(orderText, orderData) {
            const regionMapping = [
                { code: 'kl', keywords: ['kuala lumpur', 'kl', 'selangor', '吉隆坡', '雪兰莪'] },
                { code: 'penang', keywords: ['penang', '槟城'] },
                { code: 'jb', keywords: ['johor', 'jb', '柔佛', '新山'] }, 
                { code: 'sabah', keywords: ['sabah', '沙巴', '亚庇', 'kota kinabalu', '仙本那'] },
                { code: 'sg', keywords: ['singapore', 'changi', '新加坡', '樟宜机场'] }
            ];
            
            const lowerOrderText = orderText.toLowerCase();
            let regionChanged = false;
            
            // 查找匹配的驾驶区域
            for (const region of regionMapping) {
                if (region.keywords.some(keyword => lowerOrderText.includes(keyword))) {
                    regionChanged = orderData['driving-region'] !== region.code;
                    orderData['driving-region'] = region.code;
                    return;
                }
            }
            
            // 默认设置为吉隆坡
            orderData['driving-region'] = 'kl';
        }

        /**
         * @function processAddress - 优化后的地址翻译函数
         * @param {string} address - 原始地址
         * @returns {Promise<string>} 翻译后的地址
         * @description 实现批量翻译、智能检测和性能优化的地址翻译系统
         */
        async function processAddress(address) {
            if (!address) return address;

            const startTime = performance.now();
            console.log(`🚀 开始处理地址: "${address}"`);

            try {
                // 步骤1: 检测是否已经是英文/马来语地址
                if (isEnglishMalayAddress(address)) {
                    console.log(`✅ 检测到英文/马来语地址，跳过翻译: ${address}`);
                    logProcessingTime(startTime, 'English/Malay Detection');
                    return address;
                }

                // 步骤2: 检查机场关键词映射
                const airportResult = processAirportKeywords(address);
                if (airportResult !== address) {
                    console.log(`✅ 机场关键词匹配成功: ${address} -> ${airportResult}`);
                    logProcessingTime(startTime, 'Airport Keywords');
                    return airportResult;
                }

                // 步骤3: 检测并处理酒店名称
                const hotelDetection = detectHotelInAddress(address);
                if (hotelDetection.hasHotel) {
                    console.log(`🏨 检测到酒店地址，使用专门的酒店翻译逻辑`);
                    const hotelResult = await processHotelAddress(address, hotelDetection);
                    if (hotelResult !== address) {
                        console.log(`✅ 酒店地址处理成功: ${address} -> ${hotelResult}`);
                        logProcessingTime(startTime, 'Hotel Translation');
                        return hotelResult;
                    }
                }

                // 步骤4: 检查完整地址的本地缓存
                const cachedResult = getFromCache(address);
                if (cachedResult) {
                    console.log(`✅ 缓存命中: ${address} -> ${cachedResult}`);
                    logProcessingTime(startTime, 'Cache Hit');
                    return cachedResult;
                }

                // 步骤5: 尝试批量翻译整个地址
                const batchResult = await batchTranslateAddress(address);
                if (batchResult !== address) {
                    console.log(`✅ 批量翻译成功: ${address} -> ${batchResult}`);
                    cacheTranslation(address, batchResult);
                    logProcessingTime(startTime, 'Batch Translation');
                    return batchResult;
                }

                // 步骤6: 智能分段翻译（仅翻译中文部分）
                const smartResult = await smartSegmentTranslation(address);
                if (smartResult !== address) {
                    console.log(`✅ 智能分段翻译成功: ${address} -> ${smartResult}`);
                    cacheTranslation(address, smartResult);
                    logProcessingTime(startTime, 'Smart Segmentation');
                    return smartResult;
                }

                console.log(`⚠️ 所有翻译方法均失败，返回原地址: ${address}`);
                logProcessingTime(startTime, 'No Translation');
                return address;

            } catch (error) {
                console.error('❌ 地址翻译异常:', error);
                logProcessingTime(startTime, 'Error');
                return address;
            }
        }

        // 填充订单表单
        async function fillOrderForm(orderData) {
            // 车型字段优先使用vehicle-type，其次car-type
            const carTypeValue = orderData['vehicle-type'] || orderData['car-type'] || '';
            document.getElementById('car-type').value = carTypeValue;
            
            // 确保地址字段正确显示翻译后的内容
            const pickupAddress = orderData['pickup-address'] || '';
            const dropoffAddress = orderData['dropoff-address'] || '';
            
            // 输出调试信息
            console.log('填充表单中的地址信息:');
            console.log('接机地址:', pickupAddress);
            console.log('送机地址:', dropoffAddress);
            
            // 其他字段保持不变...
            document.getElementById('ota').value = orderData.ota || '';
            document.getElementById('ota-reference').value = orderData['ota-reference'] || '';
            document.getElementById('price').value = orderData.price || '';
            document.getElementById('name').value = orderData.name || '';
            document.getElementById('phone').value = orderData.phone || '';
            document.getElementById('email').value = orderData.email || '';
            document.getElementById('flight-number').value = orderData['flight-number'] || '';
            document.getElementById('pickup-datetime').value = orderData['pickup-datetime'] || '';
            
            // 强制更新地址字段
            const pickupAddressInput = document.getElementById('pickup-address');
            pickupAddressInput.value = pickupAddress;
            // 触发输入事件以确保值被正确应用
            pickupAddressInput.dispatchEvent(new Event('input', { bubbles: true }));
            
            const dropoffAddressInput = document.getElementById('dropoff-address');
            dropoffAddressInput.value = dropoffAddress;
            dropoffAddressInput.dispatchEvent(new Event('input', { bubbles: true }));
            
            document.getElementById('passenger-number').value = orderData['passenger-number'] || '';
            document.getElementById('luggage-number').value = orderData['luggage-number'] || '';
            document.getElementById('language').value = orderData.language || '';
            // 处理category选择框
            const categorySelect = document.getElementById('category');
            const categoryWasDisabled = categorySelect.disabled;
            categorySelect.disabled = false;
            categorySelect.value = orderData.category || '';
            categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
            categorySelect.disabled = categoryWasDisabled;
            
            // 处理subcategory选择框
            const subcategorySelect = document.getElementById('subcategory');
            const subcategoryWasDisabled = subcategorySelect.disabled;
            subcategorySelect.disabled = false;

            // 🔧 Fix: 智能设置 subcategory 值，确保包车服务正确设置
            let subcategoryValue = orderData.subcategory;
            if (!subcategoryValue || subcategoryValue.trim() === '') {
                // 如果 subcategory 为空，根据 category 智能设置
                if (orderData.category === 'Charter 包车') {
                    subcategoryValue = 'charter';
                    console.log('🔧 populateFormFields: 智能设置 subcategory=charter');
                } else if (orderData.category === 'airport') {
                    subcategoryValue = 'pickup'; // 默认接机
                    console.log('🔧 populateFormFields: 智能设置 subcategory=pickup');
                } else {
                    subcategoryValue = '';
                }
            }

            subcategorySelect.value = subcategoryValue;
            subcategorySelect.dispatchEvent(new Event('change', { bubbles: true }));
            subcategorySelect.disabled = subcategoryWasDisabled;
            // 处理驾驶区域选择框
            const drivingRegionSelect = document.getElementById('driving-region');
            // 临时移除disabled属性以便设置值
            const wasDisabled = drivingRegionSelect.disabled;
            drivingRegionSelect.disabled = false;
            drivingRegionSelect.value = orderData['driving-region'] || 'kl';
            // 触发change事件确保值被正确应用
            drivingRegionSelect.dispatchEvent(new Event('change', { bubbles: true }));
            // 恢复disabled状态
            drivingRegionSelect.disabled = wasDisabled;
            
            // 输出调试信息
            console.log('驾驶区域:', orderData['driving-region']);
            document.getElementById('driver').value = orderData.driver || '';
            document.getElementById('remark').value = orderData.remark || '';
        }

        // 创建订单元素
        function createOrderElement(orderData, rawText, index) {
            const currentLang = languageSelector.value;
            const orderDiv = document.createElement('div');
            orderDiv.className = 'order-item';
            
            // 添加响应式类，用于移动端适配
            const applyMobileClass = () => {
                if (window.matchMedia('(max-width: 767px)').matches) {
                    orderDiv.classList.add('order-item-mobile');
                } else {
                    orderDiv.classList.remove('order-item-mobile');
                }
            };
            
            // 初始应用
            applyMobileClass();
            
            // 使用防抖函数优化resize事件
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(applyMobileClass, 100);
            });
            
            // 创建订单标题
            const header = document.createElement('div');
            header.style.display = 'flex';
            header.style.justifyContent = 'space-between';
            header.style.alignItems = 'center';
            header.style.marginBottom = '1rem';
            header.style.paddingBottom = '0.5rem';
            header.style.borderBottom = '1px solid #eee';
            
            const title = document.createElement('h3');
            title.style.fontSize = '1rem';
            title.style.fontWeight = '500';
            title.style.margin = '0';
            title.textContent = `${i18n[currentLang]['order-number']} #${index} - ${orderData['ota-reference'] || '未知编号'}`;
            
            const copyButton = document.createElement('button');
            copyButton.className = 'btn btn-gray';
            copyButton.style.padding = '0.25rem 0.5rem';
            copyButton.style.fontSize = '0.875rem';
            copyButton.textContent = i18n[currentLang]['copy'];
            copyButton.addEventListener('click', () => copyOrderData(orderData));
            
            header.appendChild(title);
            header.appendChild(copyButton);
            orderDiv.appendChild(header);
            
            // 创建订单详情
            const details = document.createElement('div');
            details.style.display = 'grid';
            details.style.gridTemplateColumns = 'repeat(2, 1fr)';
            details.style.gap = '0.5rem';
            details.style.fontSize = '0.875rem';
            
            // 响应式布局 - 在移动端使用单列布局
            const updateGridLayout = () => {
                if (window.matchMedia('(max-width: 767px)').matches) {
                    details.style.gridTemplateColumns = '1fr';
                } else {
                    details.style.gridTemplateColumns = 'repeat(2, 1fr)';
                }
            };
            
            // 初始应用
            updateGridLayout();
            
            // 使用防抖函数优化resize事件
            let layoutResizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(layoutResizeTimeout);
                layoutResizeTimeout = setTimeout(updateGridLayout, 100);
            });
            
            // 添加字段
            const fieldGroups = [
                { label: i18n[currentLang]['ota-platform'], value: orderData.ota },
                { label: i18n[currentLang]['order-number'], value: orderData['ota-reference'] },
                { label: i18n[currentLang]['price'], value: orderData.price },
                { label: i18n[currentLang]['passenger-name'], value: orderData.name },
                { label: i18n[currentLang]['phone'], value: orderData.phone },
                { label: i18n[currentLang]['flight-number'], value: orderData['flight-number'] },
                { label: i18n[currentLang]['pickup-time'], value: orderData['pickup-datetime'] },
                { label: i18n[currentLang]['pickup-address'], value: orderData['pickup-address'] },
                { label: i18n[currentLang]['dropoff-address'], value: orderData['dropoff-address'] },
                { label: i18n[currentLang]['car-type'], value: orderData['vehicle-type'] || orderData['car-type'] },
                { label: i18n[currentLang]['passenger-count'], value: orderData['passenger-number'] },
                { label: i18n[currentLang]['order-type'], value: getOrderTypeLabel(orderData, currentLang) },
                { label: i18n[currentLang]['driving-region'], value: getDrivingRegionLabel(orderData['driving-region']) },
                { label: i18n[currentLang]['remarks'], value: orderData.remark }
            ];
            
            fieldGroups.forEach(field => {
                if (field.value) {
                    const fieldDiv = document.createElement('div');
                    fieldDiv.style.display = 'flex';
                    
                    const label = document.createElement('div');
                    label.style.fontWeight = '500';
                    label.style.width = '6rem';
                    label.textContent = field.label + '：';
                    
                    const value = document.createElement('div');
                    value.style.flex = '1';
                    value.textContent = field.value;
                    
                    fieldDiv.appendChild(label);
                    fieldDiv.appendChild(value);
                    details.appendChild(fieldDiv);
                }
            });
            
            orderDiv.appendChild(details);
            
            // 添加查看详细按钮
            const viewDetailsButton = document.createElement('button');
            viewDetailsButton.className = 'btn btn-primary';
            viewDetailsButton.style.marginTop = '1rem';
            viewDetailsButton.style.width = '100%';
            viewDetailsButton.textContent = i18n[currentLang]['view-details'];
            viewDetailsButton.addEventListener('click', () => viewDetailedOrder(orderData, rawText));
            
            orderDiv.appendChild(viewDetailsButton);
            
            return orderDiv;
        }

        // 获取订单类型标签（多语言支持）
        function getOrderTypeLabel(orderData, lang) {
            if (orderData.category === 'airport') {
                if (orderData.subcategory === 'pickup') {
                    return i18n[lang]['airport-pickup'];
                } else if (orderData.subcategory === 'dropoff') {
                    return i18n[lang]['airport-dropoff'];
                }
            }
            return i18n[lang]['charter'];
        }
        
        // 获取驾驶区域标签
        function getDrivingRegionLabel(regionCode) {
            const regionMap = {
                'kl': '吉隆坡',
                'penang': '槟城',
                'sg': '新加坡',
                'jb': '新山',
                'sabah': '沙巴'
            };
            
            if (!regionCode) return '';
            
            // 尝试小写匹配
            const lowerCode = regionCode.toLowerCase();
            if (regionMap[lowerCode]) {
                return regionMap[lowerCode];
            }
            
            // 如果没有匹配到，返回原始代码
            return regionCode.toUpperCase();
        }

        // 查看详细订单
        async function viewDetailedOrder(orderData, rawText) {
            // 显示标准订单表单，隐藏多订单容器
            document.querySelector('.grid').classList.remove('hidden');
            multiOrdersContainer.classList.add('hidden');
            
            // 确保地址已翻译
            if (orderData['pickup-address'] && !orderData['pickup-address-translated']) {
                orderData['pickup-address'] = await processAddress(orderData['pickup-address']);
                orderData['pickup-address-translated'] = true;
            }
            
            if (orderData['dropoff-address'] && !orderData['dropoff-address-translated']) {
                orderData['dropoff-address'] = await processAddress(orderData['dropoff-address']);
                orderData['dropoff-address-translated'] = true;
            }
            
            // 填充表单
            fillOrderForm(orderData);
            
            // 显示原始文本供参考
            rawOrderTextarea.value = rawText;
        }

        // 启用表单字段编辑
        function enableFormEditing() {
            formInputs.forEach(input => {
                input.readOnly = false;
                input.disabled = false;
            });
            
            editBtn.classList.add('hidden');
            saveBtn.classList.remove('hidden');
        }

        // 禁用表单字段编辑
        function disableFormEditing() {
            formInputs.forEach(input => {
                input.readOnly = true;
                input.disabled = true;
            });
            
            saveBtn.classList.add('hidden');
            editBtn.classList.remove('hidden');
        }

        // 复制表单数据
        function copyFormData() {
            let formattedData = '';
            
            formInputs.forEach(input => {
                if (input.id && input.value) {
                    const label = input.previousElementSibling?.textContent || input.id;
                    formattedData += `${label}: ${input.value}\n`;
                }
            });
            
            copyToClipboard(formattedData);
            showNotification(i18n[languageSelector.value]['data-copied']);
        }

        // 复制订单数据
        function copyOrderData(orderData) {
            let formattedData = '';
            
            // 字段映射
            const fieldMapping = {
                'ota': 'OTA平台',
                'ota-reference': 'OTA订单号',
                'price': '价格',
                'name': '乘客姓名',
                'phone': '电话',
                'email': '邮箱',
                'flight-number': '航班号',
                'pickup-datetime': '接机时间',
                'pickup-address': '接机地址',
                'dropoff-address': '送机地址',
                'car-type': '车型',
                'vehicle-type': '车型',
                'luggage-number': '行李数量',
                'passenger-number': '乘客人数',
                'language': '语言',
                'category': '类别',
                'subcategory': '子类别',
                'driving-region': '驾驶区域',
                'driver': '司机数量',
                'remark': '备注'
            };
            
            // 处理订单类型信息
            const currentLang = languageSelector.value;
            
            // 添加订单类型字段
            let orderTypeDisplay = '';
            
            // 根据类别和子类别确定订单类型
            if (orderData.category === 'airport') {
                if (orderData.subcategory === 'pickup') {
                    orderTypeDisplay = i18n[currentLang]['airport-pickup'] || '机场接机';
                } else if (orderData.subcategory === 'dropoff') {
                    orderTypeDisplay = i18n[currentLang]['airport-dropoff'] || '机场送机';
                } else {
                    orderTypeDisplay = '机场接送';
                }
            } else {
                orderTypeDisplay = i18n[currentLang]['charter'] || '包车服务';
            }
            
            // 添加类别和子类别字段
            if (orderData.category) {
                formattedData += `${fieldMapping['category']}: ${orderData.category}\n`;
            }
            
            if (orderData.subcategory && orderData.category === 'airport') {
                formattedData += `${fieldMapping['subcategory']}: ${orderData.subcategory}\n`;
            }
            
            // 添加订单类型字段
            formattedData += `${fieldMapping['order-type']}: ${orderTypeDisplay}\n`;
            
            // 格式化数据
            for (const [key, value] of Object.entries(orderData)) {
                // 跳过vehicle-type如果car-type已经存在且相同
                if (key === 'vehicle-type' && orderData['car-type'] === value) {
                    continue;
                }
                
                // 跳过已经处理的订单类型相关字段
                if (key === 'order-type' || key === 'category' || key === 'subcategory') {
                    continue;
                }
                
                if (value && fieldMapping[key]) {
                    let displayValue = value;
                    
                    if (key === 'driving-region') {
                        displayValue = getDrivingRegionLabel(value);
                    }
                    
                    formattedData += `${fieldMapping[key]}: ${displayValue}\n`;
                }
            }
            
            copyToClipboard(formattedData);
            showNotification(i18n[languageSelector.value]['data-copied']);
        }

        // 复制内容到剪贴板
        function copyToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }

        // 显示通知
        function showNotification(message) {
            notification.textContent = message;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        /**
         * @function showApiLoader - 显示API加载动画
         * @description 在调用DeepSeek API时显示加载动画
         */
        function showApiLoader() {
            const loader = document.getElementById('api-loader');
            if (loader) {
                loader.style.display = 'flex';
                // 使用setTimeout确保display设置生效后再添加show类
                setTimeout(() => {
                    loader.classList.add('show');
                }, 10);
            }
        }

        /**
         * @function hideApiLoader - 隐藏API加载动画
         * @description 在API调用完成后隐藏加载动画
         */
        function hideApiLoader() {
            const loader = document.getElementById('api-loader');
            if (loader) {
                loader.classList.remove('show');
                // 等待动画完成后隐藏元素
                setTimeout(() => {
                    loader.style.display = 'none';
                }, 300);
            }
        }

        // 事件监听器
        convertBtn.addEventListener('click', async () => {
            await convertOrder();
        });
        
        resetBtn.addEventListener('click', function() {
            rawOrderTextarea.value = '';
        });
        
        editBtn.addEventListener('click', function() {
            enableFormEditing();
            showNotification(i18n[languageSelector.value]['edit-enabled']);
        });
        
        saveBtn.addEventListener('click', function() {
            disableFormEditing();
            showNotification(i18n[languageSelector.value]['changes-saved']);
        });
        
        copyOutputBtn.addEventListener('click', copyFormData);

        // 语言选择器事件
        languageSelector.addEventListener('change', function() {
            const selectedLang = this.value;
            updateUILanguage(selectedLang);
        });

        // 页面加载时检查是否有来自sessionStorage的订单数据并处理
        document.addEventListener('DOMContentLoaded', function() {
            // 设置初始语言
            const savedLang = localStorage.getItem('preferred-language') || 'zh';
            languageSelector.value = savedLang;
            updateUILanguage(savedLang);
            
            // 应用设备特定样式
            applyDeviceSpecificStyles();
            
            const orderData = sessionStorage.getItem('orderData');
            if (orderData) {
                rawOrderTextarea.value = orderData;
                // 清除sessionStorage中的数据
                sessionStorage.removeItem('orderData');
                // 自动处理订单
                convertBtn.click();
            }
            
            // 监听窗口大小变化
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(applyDeviceSpecificStyles, 100);
            });
        });

        // 移除重复的事件监听器（已在上面定义）
        // 这个重复的事件监听器已被移除，避免冲突
        
        // 设备特定样式应用函数
        function applyDeviceSpecificStyles() {
            const isMobile = window.matchMedia('(max-width: 767px)').matches;
            
            // 应用移动端特定样式
            if (isMobile) {
                // 调整按钮大小和触摸目标
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.style.minHeight = '44px';
                });
                
                // 调整表单元素大小
                document.querySelectorAll('.form-input, textarea, select').forEach(input => {
                    input.style.fontSize = '16px';
                    input.style.padding = '0.625rem';
                });
                
                // 调整多订单容器
                const ordersList = document.getElementById('orders-list');
                if (ordersList) {
                    ordersList.style.gridTemplateColumns = '1fr';
                    ordersList.style.gap = '0.75rem';
                    ordersList.style.padding = '0.75rem';
                }
                
                // 添加移动端类到订单项
                document.querySelectorAll('.order-item').forEach(item => {
                    item.classList.add('order-item-mobile');
                });
            } else {
                // 恢复桌面端样式
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.style.minHeight = '';
                });
                
                document.querySelectorAll('.form-input, textarea, select').forEach(input => {
                    input.style.fontSize = '';
                    input.style.padding = '';
                });
                
                const ordersList = document.getElementById('orders-list');
                if (ordersList) {
                    ordersList.style.gridTemplateColumns = 'repeat(auto-fill, minmax(280px, 1fr))';
                    ordersList.style.gap = '1rem';
                    ordersList.style.padding = '1rem';
                }
                
                // 移除移动端类
                document.querySelectorAll('.order-item').forEach(item => {
                    item.classList.remove('order-item-mobile');
                });
            }
        }
        
        // API密钥配置 - 使用统一配置文件
        // 注意：API密钥现在通过api-config.js统一管理
        
        /**
         * @function parseOrderWithDeepSeek - 使用DeepSeek API进行智能订单解析
         * @param {string} orderText - 原始订单文本
         * @returns {Promise<Object>} 解析后的标准化订单数据
         * @description 使用DeepSeek API将原始订单文本解析为标准化的JSON格式数据
         */
        async function parseOrderWithDeepSeek(orderText) {
            if (!orderText || orderText.trim().length === 0) {
                throw new Error('订单文本为空');
            }

            try {
                // 构建专门的系统提示词
                const systemPrompt = `你是一个专业的订单数据提取助手。请从中文订单文本中提取结构化数据，并返回严格的JSON格式。

提取规则：
1. 金额计算：从"商铺订单X元"中提取数字，乘以0.61，保留2位小数
2. 地区映射：吉隆坡→kl, 新加坡→sg, 槟城→penang, 新山→jb, 沙巴→sabah
3. 订单类型：
   - 接机服务：接机→airport/pickup
   - 送机服务：送机→airport/dropoff
   - 包车服务：包车、一日游、半日游、城市游览、观光游、旅游包车→charter/charter
4. 车型标准化：5座车→sedan, 7座车→SUV, 商务七座→Serena, 豪华七座→Alphard/Velfire
5. 时间格式：日期+时间组合为"YYYY-MM-DD HH:MM"格式

必须返回以下JSON格式，缺失字段用空字符串：
{
  "ota": "Jing Ge",
  "ota-reference": "",
  "price": "",
  "name": "",
  "flight-number": "",
  "pickup-datetime": "",
  "pickup-address": "",
  "dropoff-address": "",
  "car-type": "",
  "category": "",
  "subcategory": "",
  "driving-region": ""
}`;

                const response = await fetch(
                    "https://api.deepseek.com/chat/completions",
                    {
                        method: "POST",
                        headers: {
                            "Authorization": `Bearer ${window.getDeepSeekApiKey()}`,
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify({
                            model: "deepseek-chat",
                            messages: [
                                {
                                    role: "system",
                                    content: systemPrompt
                                },
                                {
                                    role: "user",
                                    content: `请解析以下订单文本：\n\n${orderText}`
                                }
                            ],
                            temperature: 0.1, // 降低随机性，提高解析准确性
                            max_tokens: 500,
                            stream: false
                        })
                    }
                );

                const data = await response.json();
                console.log("DeepSeek API订单解析返回结果:", data);

                if (data && data.choices && data.choices.length > 0 && data.choices[0].message && data.choices[0].message.content) {
                    let result = data.choices[0].message.content.trim();
                    console.log('原始解析结果:', result);

                    // 提取JSON内容
                    const jsonMatch = result.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                        result = jsonMatch[0];
                    }

                    // 解析JSON
                    const parsedData = JSON.parse(result);
                    console.log('解析后的订单数据:', parsedData);

                    // 保存原始文本用于电话号码提取
                    parsedData.originalText = orderText;

                    // 数据验证和清洗
                    return validateAndCleanOrderData(parsedData, parsedData);
                } else {
                    throw new Error('无法从DeepSeek API响应中提取解析结果');
                }
            } catch (error) {
                console.error("DeepSeek API订单解析失败:", error);
                throw error;
            }
        }
        
        /**
         * @function validateAndCleanOrderData - 验证和清洗订单数据
         * @param {Object} orderData - 原始订单数据
         * @param {Object} originalData - 包含原始文本的数据对象（可选）
         * @returns {Object} 清洗后的订单数据
         * @description 对DeepSeek API返回的订单数据进行验证、清洗和标准化处理，包含电话号码和车型增强功能
         */
        function validateAndCleanOrderData(orderData, originalData = null) {
            let cleanedData = {
                ota: 'Jing Ge',
                'ota-reference': '',
                price: '',
                name: '',
                phone: '',
                email: '',
                'flight-number': '',
                'pickup-datetime': '',
                'pickup-address': '',
                'dropoff-address': '',
                'car-type': '',
                'passenger-number': '1',
                'luggage-number': '1',
                language: 'Chinese',
                category: 'airport',
                subcategory: 'pickup',
                'driving-region': 'kl',
                driver: '1',
                remark: ''
            };

            // 复制有效字段（特殊处理 category 和 subcategory）
            Object.keys(cleanedData).forEach(key => {
                if (orderData[key] !== undefined && orderData[key] !== null) {
                    const value = orderData[key].toString().trim();
                    if (value !== '') {
                        // 对于 subcategory，只有在有意义的值时才复制
                        if (key === 'subcategory' && (value === '' || value === 'undefined' || value === 'null')) {
                            // 跳过无效的 subcategory，保持默认值
                            console.log(`🔧 跳过无效的 subcategory: "${value}"`);
                            return;
                        }
                        cleanedData[key] = value;
                    }
                }
            });

            // 特殊字段处理
            // 价格验证和格式化
            if (cleanedData.price) {
                const priceNum = parseFloat(cleanedData.price);
                if (!isNaN(priceNum)) {
                    cleanedData.price = priceNum.toFixed(2);
                }
            }

            // 时间格式验证
            if (cleanedData['pickup-datetime']) {
                // 确保时间格式正确
                const timeMatch = cleanedData['pickup-datetime'].match(/(\d{4}-\d{2}-\d{2})\s*(\d{1,2}):?(\d{2})/);
                if (timeMatch) {
                    cleanedData['pickup-datetime'] = `${timeMatch[1]} ${timeMatch[2].padStart(2, '0')}:${timeMatch[3]}`;
                }
            }

            // 驾驶区域验证
            const validRegions = ['kl', 'sg', 'penang', 'jb', 'sabah'];
            if (!validRegions.includes(cleanedData['driving-region'])) {
                cleanedData['driving-region'] = 'kl'; // 默认吉隆坡
            }

            // 🔧 Enhancement 1&2: 增强类别和子类别验证逻辑
            const originalText = (originalData && originalData.originalText) ? originalData.originalText : JSON.stringify(orderData);

            // 检查包车服务关键词
            const charterKeywords = ['包车', '一日游', '半日游', '城市游览', '观光游', '旅游包车', '包车游', '市内游'];
            const hasCharterKeyword = charterKeywords.some(keyword => originalText.includes(keyword));

            if (hasCharterKeyword) {
                cleanedData.category = 'Charter 包车';
                cleanedData.subcategory = 'charter';
                console.log(`🔧 验证阶段检测到包车服务，强制设置: category=Charter 包车, subcategory=charter`);
            } else if (cleanedData.category === 'airport') {
                if (!['pickup', 'dropoff'].includes(cleanedData.subcategory)) {
                    cleanedData.subcategory = 'pickup'; // 默认接机
                }
                console.log(`🔧 验证阶段确认机场服务: category=airport, subcategory=${cleanedData.subcategory}`);
            } else {
                // 如果不是明确的机场服务，默认设为包车
                cleanedData.category = 'Charter 包车';
                cleanedData.subcategory = 'charter';
                console.log(`🔧 验证阶段默认设置: category=Charter 包车, subcategory=charter`);
            }

            // 🔧 Enhancement 1: Phone Number Synchronization with Order Reference
            cleanedData = enhancePhoneNumberExtraction(cleanedData, originalData || orderData);

            // 🔧 Enhancement 2: Intelligent Car Type Recognition System
            cleanedData = enhanceCarTypeRecognition(cleanedData);

            // 🔧 Enhancement 3: Intelligent Remark Field Mapping
            cleanedData = enhanceRemarkFieldMapping(cleanedData, originalData || orderData);

            // 🔧 Enhancement 4: Charter Service Address Default Logic
            cleanedData = enhanceCharterServiceAddresses(cleanedData);

            // 🔧 Final Check: 确保 subcategory 不为空
            if (!cleanedData.subcategory || cleanedData.subcategory.trim() === '') {
                if (cleanedData.category === 'Charter 包车') {
                    cleanedData.subcategory = 'charter';
                    console.log(`🔧 最终检查：强制设置 subcategory=charter`);
                } else if (cleanedData.category === 'airport') {
                    cleanedData.subcategory = 'pickup';
                    console.log(`🔧 最终检查：强制设置 subcategory=pickup`);
                }
            }

            console.log('数据验证和清洗完成:', cleanedData);
            return cleanedData;
        }

        /**
         * @function enhancePhoneNumberExtraction - 增强电话号码提取功能
         * @param {Object} cleanedData - 已清洗的订单数据
         * @param {Object} originalData - 原始订单数据
         * @returns {Object} 增强后的订单数据
         * @description 从原始订单文本中提取电话号码，如果没有找到则使用订单号作为备用
         */
        function enhancePhoneNumberExtraction(cleanedData, originalData) {
            console.log('🔧 开始增强电话号码提取...');

            // 如果已经有电话号码，直接返回
            if (cleanedData.phone && cleanedData.phone.trim() !== '') {
                console.log(`✅ 已有电话号码: ${cleanedData.phone}`);
                return cleanedData;
            }

            // 从原始数据中提取电话号码的正则表达式模式
            const phonePatterns = [
                // 中国大陆手机号 (11位)
                /(?:手机|电话|联系方式|联系电话|phone|mobile)[：:]\s*(\+?86[-\s]?)?([1][3-9]\d{9})/i,
                // 马来西亚手机号 (10-11位，以01开头)
                /(?:手机|电话|联系方式|联系电话|phone|mobile)[：:]\s*(\+?60[-\s]?)?(01[0-9][-\s]?\d{7,8})/i,
                // 新加坡手机号 (8位，以8或9开头)
                /(?:手机|电话|联系方式|联系电话|phone|mobile)[：:]\s*(\+?65[-\s]?)?([89]\d{7})/i,
                // 通用手机号模式 (8-15位数字)
                /(?:手机|电话|联系方式|联系电话|phone|mobile)[：:]\s*(\+?\d{1,4}[-\s]?)?(\d{8,15})/i,
                // 独立的手机号码（没有标签）
                /(?:^|\s)(\+?86[-\s]?)?([1][3-9]\d{9})(?:\s|$)/,
                /(?:^|\s)(\+?60[-\s]?)?(01[0-9][-\s]?\d{7,8})(?:\s|$)/,
                /(?:^|\s)(\+?65[-\s]?)?([89]\d{7})(?:\s|$)/
            ];

            // 获取原始订单文本（如果有的话）
            const orderText = originalData.originalText || JSON.stringify(originalData);

            for (const pattern of phonePatterns) {
                const match = orderText.match(pattern);
                if (match) {
                    let phoneNumber = '';

                    // 组合国家代码和号码
                    if (match[1] && match[2]) {
                        phoneNumber = (match[1] + match[2]).replace(/[-\s]/g, '');
                    } else if (match[2]) {
                        phoneNumber = match[2].replace(/[-\s]/g, '');
                    } else if (match[1]) {
                        phoneNumber = match[1].replace(/[-\s]/g, '');
                    }

                    // 验证电话号码格式
                    if (phoneNumber && phoneNumber.length >= 8 && phoneNumber.length <= 15) {
                        cleanedData.phone = phoneNumber;
                        console.log(`✅ 从订单文本提取到电话号码: ${phoneNumber}`);
                        return cleanedData;
                    }
                }
            }

            // 如果没有找到电话号码，使用订单号作为备用
            if (cleanedData['ota-reference'] && cleanedData['ota-reference'].trim() !== '') {
                cleanedData.phone = cleanedData['ota-reference'];
                console.log(`📞 未找到电话号码，使用订单号作为备用: ${cleanedData.phone}`);
            } else {
                console.log('⚠️ 未找到电话号码且无订单号可用');
            }

            return cleanedData;
        }

        /**
         * @function enhanceCarTypeRecognition - 智能车型识别系统
         * @param {Object} cleanedData - 已清洗的订单数据
         * @returns {Object} 增强后的订单数据
         * @description 基于乘客人数智能确定车型，当车型未明确指定时
         */
        function enhanceCarTypeRecognition(cleanedData) {
            console.log('🚗 开始智能车型识别...');

            // 如果已经有明确的车型，保持不变
            if (cleanedData['car-type'] && cleanedData['car-type'].trim() !== '') {
                console.log(`✅ 已有明确车型: ${cleanedData['car-type']}`);
                return cleanedData;
            }

            // 获取乘客人数
            const passengerCount = parseInt(cleanedData['passenger-number']) || 1;
            console.log(`👥 乘客人数: ${passengerCount}`);

            // 基于乘客人数智能确定车型
            let recommendedCarType = '';

            if (passengerCount >= 1 && passengerCount <= 4) {
                recommendedCarType = 'sedan';
                console.log(`🚗 1-4人乘客，推荐车型: sedan`);
            } else if (passengerCount >= 5 && passengerCount <= 7) {
                recommendedCarType = 'mpv';
                console.log(`🚐 5-7人乘客，推荐车型: mpv`);
            } else if (passengerCount >= 8) {
                recommendedCarType = 'van';
                console.log(`🚌 8+人乘客，推荐车型: van`);
            } else {
                recommendedCarType = 'sedan';
                console.log(`🚗 默认车型: sedan`);
            }

            cleanedData['car-type'] = recommendedCarType;
            console.log(`✅ 智能车型识别完成: ${recommendedCarType}`);

            return cleanedData;
        }

        /**
         * @function enhanceRemarkFieldMapping - 智能 remark 字段映射系统
         * @param {Object} cleanedData - 已清洗的订单数据
         * @param {Object} originalData - 原始订单数据
         * @returns {Object} 增强后的订单数据
         * @description 从订单文本中智能提取路线信息、特殊需求、行程描述等填充到 remark 字段
         */
        function enhanceRemarkFieldMapping(cleanedData, originalData) {
            console.log('🔧 开始智能 remark 字段映射...');

            // 如果已经有 remark 内容，检查是否需要增强
            if (cleanedData.remark && cleanedData.remark.trim() !== '') {
                console.log(`✅ 已有 remark 内容: ${cleanedData.remark}`);
                return cleanedData;
            }

            // 获取原始订单文本
            const orderText = originalData.originalText || JSON.stringify(originalData);

            // 定义 remark 提取规则（按优先级排序）
            const remarkPatterns = [
                // 优先级1: 直接的路线描述（更精确的匹配）
                {
                    pattern: /((?:马六甲|布城|云顶|黑风洞|双子塔|吉隆坡|新加坡|槟城)[^\\n]*(?:一日游|半日游|包车游|城市游览|观光游|旅游)[^\\n]*)/i,
                    priority: 1,
                    description: '精确路线描述'
                },
                // 优先级2: 通用路线描述
                {
                    pattern: /([^\\n]*(?:一日游|半日游|包车游|城市游览|观光游|旅游)[^\\n]*)/i,
                    priority: 2,
                    description: '通用路线描述'
                },
                // 优先级3: 行程信息
                {
                    pattern: /行程[：:\\s]*([^\\n]+)/i,
                    priority: 3,
                    description: '行程信息',
                    filter: (content) => {
                        // 只有包含旅游相关关键词的行程才作为 remark
                        return /一日游|半日游|包车游|城市游览|观光游|旅游|景点|马六甲|布城|云顶|黑风洞/.test(content);
                    }
                },
                // 优先级4: 目的地信息
                {
                    pattern: /(?:目的地|前往|到达)[：:\\s]*([^\\n]+)/i,
                    priority: 4,
                    description: '目的地信息'
                },
                // 优先级5: 备注和特殊需求
                {
                    pattern: /(?:备注|说明|要求|特殊需求|注意事项)[：:\\s]*([^\\n]+)/i,
                    priority: 5,
                    description: '备注信息'
                },
                // 优先级6: 从完整描述中提取关键路线
                {
                    pattern: /(马六甲[^\\n]*|布城[^\\n]*|云顶[^\\n]*|黑风洞[^\\n]*|双子塔[^\\n]*)/i,
                    priority: 6,
                    description: '关键景点'
                }
            ];

            let bestRemark = null;
            let bestPriority = Infinity;

            // 遍历所有模式，寻找最佳匹配
            for (const patternInfo of remarkPatterns) {
                const match = orderText.match(patternInfo.pattern);
                if (match && match[1]) {
                    let content = match[1].trim();

                    // 应用过滤器（如果有）
                    if (patternInfo.filter && !patternInfo.filter(content)) {
                        console.log(`🔧 内容被过滤器排除: ${content}`);
                        continue;
                    }

                    // 清理内容
                    content = cleanRemarkContent(content);

                    if (content && patternInfo.priority < bestPriority) {
                        bestRemark = content;
                        bestPriority = patternInfo.priority;
                        console.log(`🔧 找到更好的 remark (优先级${patternInfo.priority}, ${patternInfo.description}): ${content}`);
                    }
                }
            }

            // 设置最终的 remark
            if (bestRemark) {
                cleanedData.remark = bestRemark;
                console.log(`✅ 智能 remark 映射完成: ${bestRemark}`);
            } else {
                console.log('⚠️ 未找到合适的 remark 内容');
            }

            return cleanedData;
        }

        /**
         * @function enhanceCharterServiceAddresses - 包车服务地址默认逻辑
         * @param {Object} cleanedData - 已清洗的订单数据
         * @returns {Object} 增强后的订单数据
         * @description 为包车服务设置默认地址逻辑，确保上车和下车地址的一致性
         */
        function enhanceCharterServiceAddresses(cleanedData) {
            console.log('🚗 开始包车服务地址默认逻辑处理...');

            // 只对包车服务进行地址处理
            if (cleanedData.category !== 'Charter 包车') {
                console.log(`✅ 非包车服务 (${cleanedData.category})，跳过地址处理`);
                return cleanedData;
            }

            const pickupAddress = cleanedData['pickup-address'] || '';
            const dropoffAddress = cleanedData['dropoff-address'] || '';

            console.log(`🔧 包车服务地址处理前: pickup="${pickupAddress}", dropoff="${dropoffAddress}"`);

            // 地址处理逻辑
            if (pickupAddress && !dropoffAddress) {
                // 只有上车地址，将下车地址设置为相同值
                cleanedData['dropoff-address'] = pickupAddress;
                console.log(`🔧 设置下车地址与上车地址相同: "${pickupAddress}"`);
            } else if (!pickupAddress && dropoffAddress) {
                // 只有下车地址，将上车地址设置为相同值
                cleanedData['pickup-address'] = dropoffAddress;
                console.log(`🔧 设置上车地址与下车地址相同: "${dropoffAddress}"`);
            } else if (!pickupAddress && !dropoffAddress) {
                // 两个地址都为空，保持为空
                console.log(`🔧 两个地址都为空，保持原状`);
            } else {
                // 两个地址都有值，保持原值不变
                console.log(`🔧 两个地址都有值，保持原值不变`);
            }

            console.log(`🔧 包车服务地址处理后: pickup="${cleanedData['pickup-address']}", dropoff="${cleanedData['dropoff-address']}"`);
            console.log(`✅ 包车服务地址默认逻辑处理完成`);

            return cleanedData;
        }

        /**
         * @function cleanRemarkContent - 清理 remark 内容
         * @param {string} content - 原始内容
         * @returns {string} 清理后的内容
         * @description 去除多余的描述词，提取核心路线信息
         */
        function cleanRemarkContent(content) {
            if (!content) return '';

            console.log(`🔧 清理前的 remark 内容: "${content}"`);

            // 去除常见的前缀词和无关信息
            const prefixesToRemove = [
                /^商铺订单\d+元\s*/,
                /^\d+座车\s*/,
                /^7座车\s*/,
                /^包车\s*/,
                /^布城\s*/,
                /^行程[：:\\s]*/,
                /^目的地[：:\\s]*/,
                /^前往[：:\\s]*/,
                /^到达[：:\\s]*/
            ];

            // 去除后缀的无关信息
            const suffixesToRemove = [
                /\s*订单号\d+.*$/,
                /\s*❇.*$/,
                /\s*\d+座车.*$/,
                /\s*7座车.*$/
            ];

            let cleaned = content;

            // 应用前缀清理
            for (const prefix of prefixesToRemove) {
                cleaned = cleaned.replace(prefix, '');
            }

            // 应用后缀清理
            for (const suffix of suffixesToRemove) {
                cleaned = cleaned.replace(suffix, '');
            }

            // 去除多余的空格和标点
            cleaned = cleaned.replace(/\s+/g, ' ').trim();

            // 特殊处理：如果包含核心景点名称，提取核心部分
            const coreAttractions = ['马六甲一日游', '布城一日游', '云顶一日游', '黑风洞一日游'];
            for (const attraction of coreAttractions) {
                if (cleaned.includes(attraction)) {
                    cleaned = attraction;
                    break;
                }
            }

            // 如果内容太短，返回空
            if (cleaned.length < 2) {
                console.log(`🔧 内容太短，返回空: "${cleaned}"`);
                return '';
            }

            // 如果内容太长，智能截取
            if (cleaned.length > 30) {
                // 优先保留包含关键词的部分
                const keywordMatch = cleaned.match(/(马六甲|布城|云顶|黑风洞|双子塔)[^，。]*(?:一日游|半日游|包车游|城市游览|观光游|旅游)?/);
                if (keywordMatch) {
                    cleaned = keywordMatch[0];
                } else {
                    // 截取前30个字符，在合适的位置断开
                    const truncated = cleaned.substring(0, 30);
                    const lastSpace = truncated.lastIndexOf(' ');
                    const lastComma = truncated.lastIndexOf('，');
                    const breakPoint = Math.max(lastSpace, lastComma);

                    if (breakPoint > 10) {
                        cleaned = truncated.substring(0, breakPoint);
                    } else {
                        cleaned = truncated;
                    }
                }
            }

            console.log(`🔧 清理后的 remark 内容: "${cleaned}"`);
            return cleaned;
        }

        /**
         * @function queryDeepSeekPOIEnglishName - 使用DeepSeek API翻译地址名称
         * @param {string} chineseName - 中文地址名称
         * @returns {Promise<string>} 翻译后的英文地址名称
         * @description 专门用于地址翻译的DeepSeek API调用函数
         */
        async function queryDeepSeekPOIEnglishName(chineseName) {
            if (!chineseName || chineseName.trim().length === 0) return chineseName;

            try {
                const response = await fetch(
                    "https://api.deepseek.com/chat/completions",
                    {
                        method: "POST",
                        headers: {
                            "Authorization": `Bearer ${window.getDeepSeekApiKey()}`,
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify({
                            model: "deepseek-chat",
                            messages: [
                                {
                                    role: "system",
                                    content: "You are a professional translator specializing in Chinese to English address translation. Always respond with ONLY the English address/location name, no explanations or additional text."
                                },
                                {
                                    role: "user",
                                    content: `请将以下中文地址翻译为准确的英文地址: ${chineseName}。只返回英文地址，不要包含其他说明。`
                                }
                            ],
                            temperature: 0.2,
                            max_tokens: 100,
                            stream: false
                        })
                    }
                );

                const data = await response.json();
                console.log("DeepSeek API地址翻译返回结果:", data);

                if (data && data.choices && data.choices.length > 0 && data.choices[0].message && data.choices[0].message.content) {
                    let result = data.choices[0].message.content.trim();
                    console.log('原始翻译结果:', result);

                    // 清理结果
                    result = result.replace(/^["']|["']$/g, ''); // 去除引号

                    if (result.includes('\n')) {
                        result = result.split('\n')[0]; // 只取第一行
                    }

                    // 处理可能的前缀文本
                    const prefixes = ['英文地址：', '英文地址:', 'English address:', 'Address:', 'Translation:'];
                    for (const prefix of prefixes) {
                        if (result.startsWith(prefix)) {
                            result = result.substring(prefix.length).trim();
                            break;
                        }
                    }

                    // 基本验证
                    if (!result || result.trim() === '' || /[\u4e00-\u9fa5]/.test(result)) {
                        throw new Error('翻译结果无效');
                    }

                    console.log('处理后的翻译结果:', result);
                    return result;
                } else {
                    throw new Error('无法从DeepSeek API响应中提取翻译结果');
                }
            } catch (error) {
                console.error("DeepSeek API地址翻译失败:", error);
                throw error;
            }
        }
        
        /**
         * @function extractOrderDataWithRegex - 使用正则表达式提取订单数据（回退方案）
         * @param {string} orderText - 原始订单文本
         * @returns {Object} 提取的订单数据
         * @description 当DeepSeek API失败时使用的正则表达式回退解析方案
         */
        function extractOrderDataWithRegex(orderText) {
            console.log('使用正则表达式回退方案解析订单');
            const orderData = {
                ota: 'Jing Ge',
                'ota-reference': '',
                price: '',
                name: '',
                phone: '',
                email: '',
                'flight-number': '',
                'pickup-datetime': '',
                'pickup-address': '',
                'dropoff-address': '',
                'car-type': '',
                'passenger-number': '1',
                'luggage-number': '1',
                language: 'Chinese',
                category: 'airport',
                subcategory: 'pickup',
                'driving-region': 'kl',
                driver: '1',
                remark: ''
            };

            // 使用更新后的字段映射规则
            let remarkParts = []; // 收集remark相关信息

            for (const mapping of jingGeRule.fieldMappings) {
                const regex = new RegExp(mapping.pattern, 'i');
                const match = orderText.match(regex);

                if (match) {
                    let value = match[1];

                    // 应用转换函数
                    if (mapping.transform && typeof mapping.transform === 'function') {
                        if (mapping.field === 'pickup-datetime' && match[2]) {
                            // 特殊处理日期时间组合
                            value = mapping.transform(match[1], match[2]);
                        } else if (mapping.field === 'service-type') {
                            // 处理服务类型映射
                            const serviceInfo = mapping.transform(value);
                            orderData.category = serviceInfo.category;
                            orderData.subcategory = serviceInfo.subcategory;
                            console.log(`🔧 服务类型识别: ${value} → category: ${serviceInfo.category}, subcategory: ${serviceInfo.subcategory}`);
                            continue; // 跳过当前循环，不设置字段值
                        } else {
                            value = mapping.transform(value);
                        }
                    }

                    // 🔧 Enhancement 3: 处理 remark 相关字段
                    if (mapping.field === 'remark') {
                        remarkParts.push({ priority: 1, content: value }); // 最高优先级
                        console.log(`🔧 Remark信息收集 (优先级1): ${value}`);
                    } else if (mapping.field === 'remark-secondary') {
                        if (value) { // 只有非null值才添加
                            remarkParts.push({ priority: 2, content: value });
                            console.log(`🔧 Remark信息收集 (优先级2): ${value}`);
                        }
                    } else if (mapping.field === 'remark-notes') {
                        remarkParts.push({ priority: 3, content: value });
                        console.log(`🔧 Remark信息收集 (优先级3): ${value}`);
                    } else {
                        orderData[mapping.field] = value;
                        console.log(`正则匹配成功: ${mapping.field} = ${value}`);
                    }
                }
            }

            // 🔧 Enhancement 3: 智能组合 remark 字段
            if (remarkParts.length > 0) {
                // 按优先级排序，选择最佳的remark内容
                remarkParts.sort((a, b) => a.priority - b.priority);
                orderData.remark = remarkParts[0].content;
                console.log(`🔧 最终Remark字段: ${orderData.remark}`);
            }

            // 智能推断缺失字段
            if (!orderData['driving-region']) {
                // 从地址中推断驾驶区域
                const regionKeywords = {
                    '吉隆坡': 'kl', 'KL': 'kl', 'Kuala Lumpur': 'kl',
                    '新加坡': 'sg', 'Singapore': 'sg',
                    '槟城': 'penang', 'Penang': 'penang',
                    '新山': 'jb', 'Johor': 'jb',
                    '沙巴': 'sabah', 'Sabah': 'sabah'
                };

                for (const [keyword, region] of Object.entries(regionKeywords)) {
                    if (orderText.includes(keyword)) {
                        orderData['driving-region'] = region;
                        break;
                    }
                }
            }

            // 🔧 Enhancement 1&2: 增强服务类型推断逻辑
            const charterKeywords = ['包车', '一日游', '半日游', '城市游览', '观光游', '旅游包车', '包车游', '市内游'];
            const airportPickupKeywords = ['接机', '机场接', '到达接'];
            const airportDropoffKeywords = ['送机', '机场送', '出发送'];

            // 检查包车服务关键词（优先级最高）
            const hasCharterKeyword = charterKeywords.some(keyword => orderText.includes(keyword));
            const hasPickupKeyword = airportPickupKeywords.some(keyword => orderText.includes(keyword));
            const hasDropoffKeyword = airportDropoffKeywords.some(keyword => orderText.includes(keyword));

            if (hasCharterKeyword) {
                orderData.category = 'Charter 包车';
                orderData.subcategory = 'charter';
                console.log(`🔧 检测到包车服务关键词，设置为: category=Charter 包车, subcategory=charter`);
            } else if (hasPickupKeyword) {
                orderData.category = 'airport';
                orderData.subcategory = 'pickup';
                console.log(`🔧 检测到接机服务关键词，设置为: category=airport, subcategory=pickup`);
            } else if (hasDropoffKeyword) {
                orderData.category = 'airport';
                orderData.subcategory = 'dropoff';
                console.log(`🔧 检测到送机服务关键词，设置为: category=airport, subcategory=dropoff`);
            } else {
                // 默认保持原有逻辑
                console.log(`🔧 未检测到明确服务类型，保持默认: category=${orderData.category}, subcategory=${orderData.subcategory}`);
            }

            console.log('正则表达式解析结果:', orderData);
            return orderData;
        }
        
        /**
         * @function queryPOIEnglishName - 重构后的地址翻译函数（仅使用DeepSeek API）
         * @param {string} chineseName - 中文地址名称
         * @returns {Promise<string>} 翻译后的英文地址名称
         * @description 使用DeepSeek API进行地址翻译，包含超时和重试机制
         */
        async function queryPOIEnglishName(chineseName) {
            if (!chineseName || chineseName.trim().length === 0) return chineseName;

            // 添加超时处理
            const timeout = (ms) => new Promise((_, reject) =>
                setTimeout(() => reject(new Error('API请求超时')), ms)
            );

            try {
                console.log(`开始使用DeepSeek API翻译地址: ${chineseName}`);

                // 调用DeepSeek API进行翻译
                const result = await Promise.race([
                    queryDeepSeekPOIEnglishName(chineseName),
                    timeout(10000) // 10秒超时
                ]);

                console.log(`DeepSeek API翻译成功: ${chineseName} -> ${result}`);
                return result;

            } catch (error) {
                console.error("DeepSeek API翻译失败:", error);

                // 尝试重试一次
                try {
                    console.log('尝试重试DeepSeek API翻译...');
                    const retryResult = await Promise.race([
                        queryDeepSeekPOIEnglishName(chineseName),
                        timeout(8000) // 8秒超时
                    ]);

                    console.log(`DeepSeek API重试翻译成功: ${chineseName} -> ${retryResult}`);
                    return retryResult;

                } catch (retryError) {
                    console.error("DeepSeek API重试也失败:", retryError);
                    console.log(`返回原始中文名称: ${chineseName}`);
                    return chineseName; // 返回原始中文名称作为后备
                }
            }
        }
        
        // 酒店名称映射表（本地缓存）
        let hotelNameMapping = {};
        
        // 尝试从localStorage加载缓存的翻译结果
        try {
            const savedMapping = localStorage.getItem('hotelNameMapping');
            if (savedMapping) {
                hotelNameMapping = JSON.parse(savedMapping);
                console.log('已从本地存储加载酒店名称映射缓存');
            }
        } catch (error) {
            console.error('加载本地缓存失败:', error);
            // 初始化默认映射
            hotelNameMapping = {
                '新加坡史各士皇族酒店': 'Royal Plaza on Scotts',
                '新加坡良木园酒店': 'Goodwood Park Hotel'
                // 其他现有映射...
            };
        }
        
        // 保存酒店名称映射到localStorage
        function saveHotelNameMapping() {
            try {
                localStorage.setItem('hotelNameMapping', JSON.stringify(hotelNameMapping));
                console.log('酒店名称映射已保存到本地存储');
            } catch (error) {
                console.error('保存酒店名称映射失败:', error);
            }
        }
        
        // 获取酒店英文名称（带重试机制）
        async function getHotelEnglishName(chineseName) {
            // 参数验证
            if (!chineseName || typeof chineseName !== 'string' || chineseName.trim() === '') {
                console.warn('无效的酒店中文名称:', chineseName);
                return chineseName || '';
            }
            
            // 检查本地缓存
            if (hotelNameMapping[chineseName]) {
                console.log(`使用缓存的翻译: ${chineseName} -> ${hotelNameMapping[chineseName]}`);
                return hotelNameMapping[chineseName];
            }
            
            // 本地模糊匹配
            const fuzzyMatch = fuzzyMatchLocal(chineseName);
            if (fuzzyMatch) {
                console.log(`使用模糊匹配的翻译: ${chineseName} -> ${fuzzyMatch}`);
                hotelNameMapping[chineseName] = fuzzyMatch;
                saveHotelNameMapping();
                return fuzzyMatch;
            }
            
            // 尝试API翻译，最多重试2次
            let retries = 0;
            const maxRetries = 2;
            
            while (retries <= maxRetries) {
                try {
                    console.log(`尝试API翻译 (尝试 ${retries + 1}/${maxRetries + 1}): ${chineseName}`);
                    const englishName = await queryPOIEnglishName(chineseName);
                    
                    // 验证翻译结果
                    if (englishName && englishName !== chineseName) {
                        console.log(`API翻译成功: ${chineseName} -> ${englishName}`);
                        hotelNameMapping[chineseName] = englishName;
                        saveHotelNameMapping();
                        return englishName;
                    } else {
                        throw new Error('翻译结果无效或未变化');
                    }
                } catch (error) {
                    console.error(`API翻译失败 (尝试 ${retries + 1}/${maxRetries + 1}):`, error);
                    retries++;
                    
                    if (retries > maxRetries) {
                        console.warn(`已达到最大重试次数，返回原始中文名称: ${chineseName}`);
                        return chineseName;
                    }
                    
                    // 重试前等待一段时间
                    await new Promise(resolve => setTimeout(resolve, 1000 * retries));
                }
            }
            
            return chineseName; // 所有尝试都失败，返回原始中文名称
        }
    // 防抖函数：优化窗口大小变化事件处理
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
    
    // 设备检测函数：检测当前设备类型
    function detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobile = /iphone|ipod|android|blackberry|windows phone/g.test(userAgent);
        const isTablet = /(ipad|tablet|playbook|silk)|(android(?!.*mobile))/g.test(userAgent);
        const isIOS = /iphone|ipod|ipad/g.test(userAgent);
        const isAndroid = /android/g.test(userAgent);
        const isEdge = /edg/g.test(userAgent);
        
        return {
            isMobile,
            isTablet,
            isDesktop: !isMobile && !isTablet,
            isIOS,
            isAndroid,
            isEdge
        };
    }
    
    // 应用设备特定样式
    function applyDeviceSpecificStyles() {
        const device = detectDevice();
        const html = document.documentElement;
        
        // 清除现有设备类
        html.classList.remove('mobile', 'tablet', 'desktop', 'ios', 'android', 'edge');
        
        // 添加设备类
        if (device.isMobile) html.classList.add('mobile');
        if (device.isTablet) html.classList.add('tablet');
        if (device.isDesktop) html.classList.add('desktop');
        if (device.isIOS) html.classList.add('ios');
        if (device.isAndroid) html.classList.add('android');
        if (device.isEdge) html.classList.add('edge');
        
        // 性能优化
        if (device.isMobile || device.isTablet) {
            // 移动端优化图片加载
            document.querySelectorAll('img').forEach(img => {
                if (!img.loading) img.loading = 'lazy';
            });
            
            // 减少移动端动画复杂度
            document.querySelectorAll('.card, .order-item, .order-card').forEach(el => {
                el.style.transition = 'transform 0.2s ease-in-out';
            });
        }
        
        // Edge浏览器特定优化
        if (device.isEdge) {
            // 使用兼容性更好的渲染方式
            document.body.style.textRendering = 'auto';
        }
        
        // 检测是否支持背景模糊
        const supportsBackdropFilter = CSS.supports('backdrop-filter', 'blur(10px)') || 
                                    CSS.supports('-webkit-backdrop-filter', 'blur(10px)');
        
        // 如果不支持背景模糊，使用替代方案
        if (!supportsBackdropFilter) {
            const loader = document.getElementById('api-loader');
            if (loader) {
                loader.style.backdropFilter = 'none';
                loader.style.webkitBackdropFilter = 'none';
                loader.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            }
        }
    }
    
    // 页面加载完成后初始化
    window.addEventListener('load', function() {
        // 应用设备特定样式
        applyDeviceSpecificStyles();
        
        // 监听窗口大小变化，使用防抖函数优化性能
        window.addEventListener('resize', debounce(function() {
            applyDeviceSpecificStyles();
        }, 250));
        
        // 性能优化：延迟加载非关键资源
        setTimeout(() => {
            // 预加载常用页面
            const links = document.querySelectorAll('a[href]');
            links.forEach(link => {
                if (link.hostname === window.location.hostname) {
                    const prefetchLink = document.createElement('link');
                    prefetchLink.rel = 'prefetch';
                    prefetchLink.href = link.href;
                    document.head.appendChild(prefetchLink);
                }
            });
        }, 2000);
    });

    /**
     * @function testHotelNameTranslation - 测试酒店名称翻译功能
     * 验证本地数据库优先级和Gemini API回退机制
     */
    async function testHotelNameTranslation() {
        console.log('🧪 开始测试酒店名称翻译功能');

        const testCases = [
            // 关键修复测试
            { input: '莱恩酒店', expected: 'Sleeping Lion Hotel', priority: 'CRITICAL' },
            { input: '莱恩套房酒店', expected: 'Sleeping Lion Suites', priority: 'CRITICAL' },
            { input: '滨海湾金沙', expected: 'Marina Bay Sands', priority: 'CRITICAL' },
            { input: '富丽敦酒店', expected: 'The Fullerton Hotel Singapore', priority: 'HIGH' },
            { input: '东方大酒店', expected: 'Eastern & Oriental Hotel', priority: 'HIGH' },

            // 国际品牌测试
            { input: '万豪酒店', expected: 'Marriott Hotel', priority: 'HIGH' },
            { input: '希尔顿酒店', expected: 'Hilton Hotel', priority: 'HIGH' },
            { input: '凯悦酒店', expected: 'Hyatt Hotel', priority: 'HIGH' },

            // 本地数据库测试
            { input: '金狮酒店', expected: 'Golden Lion Hotel', priority: 'MEDIUM' },
            { input: '香格里拉酒店', expected: 'Shangri-La Hotel', priority: 'MEDIUM' }
        ];

        let passCount = 0;
        let totalCount = testCases.length;

        for (const testCase of testCases) {
            try {
                console.log(`\n🔍 测试: ${testCase.input} (${testCase.priority})`);

                const result = await translateHotelName(testCase.input);
                const passed = result === testCase.expected;

                if (passed) {
                    passCount++;
                    console.log(`✅ 通过: ${testCase.input} -> ${result}`);
                } else {
                    console.log(`❌ 失败: ${testCase.input}`);
                    console.log(`   期望: ${testCase.expected}`);
                    console.log(`   实际: ${result}`);
                }

            } catch (error) {
                console.error(`❌ 测试错误: ${testCase.input} - ${error.message}`);
            }
        }

        const passRate = (passCount / totalCount * 100).toFixed(1);
        console.log(`\n📊 测试结果: ${passCount}/${totalCount} (${passRate}%)`);

        if (passCount === totalCount) {
            console.log('🎉 所有酒店名称翻译测试通过！');
        } else {
            console.warn('⚠️ 部分测试失败，请检查配置');
        }

        return { passCount, totalCount, passRate };
    }

    // 在控制台中提供测试函数
    window.testHotelNameTranslation = testHotelNameTranslation;

    console.log('🏨 酒店名称翻译系统已加载');
    console.log('📊 本地数据库包含 300+ 酒店映射');
    console.log('🔧 使用 testHotelNameTranslation() 进行测试');
    console.log('🎯 酒店翻译工作流程: 本地数据库 → 订单提取 → Gemini API');
    console.log('🌐 地址翻译系统已更新为使用 Gemini API（根据用户偏好）');
    console.log('🔧 使用 testAddressTranslation() 测试地址翻译功能');
    console.log('📞 电话号码提取与同步功能已启用');
    console.log('🚗 智能车型识别系统已启用');
    console.log('🔧 使用 testOrderEnhancements() 测试订单增强功能');

    /**
     * @function testAddressTranslation - 测试地址翻译功能
     * 验证地址翻译是否使用Gemini API而非DeepSeek API
     */
    async function testAddressTranslation() {
        console.log('🧪 开始测试地址翻译功能 - 验证是否使用Gemini API');

        const testAddresses = [
            '莎莉亚香格里拉',
            '哥打京那巴鲁国际机场',
            '吉隆坡双子塔'
        ];

        for (const address of testAddresses) {
            try {
                console.log(`\n🔄 测试地址: ${address}`);
                const result = await queryAddressEnglishNameWithGemini(address);
                console.log(`✅ Gemini API翻译结果: ${address} -> ${result}`);
            } catch (error) {
                console.error(`❌ 翻译失败: ${address}`, error);
            }
        }

        console.log('\n🎯 地址翻译测试完成！');
    }

    // 将测试函数暴露到全局作用域
    window.testAddressTranslation = testAddressTranslation;

    /**
     * @function testOrderEnhancements - 测试订单处理增强功能
     * 验证电话号码提取和智能车型识别功能
     */
    async function testOrderEnhancements() {
        console.log('🧪 开始测试订单处理增强功能');
        console.log('📋 测试项目: 1. 电话号码提取与同步 2. 智能车型识别');

        const testCases = [
            {
                name: '测试1: 有电话号码 + 6人乘客',
                orderData: {
                    'ota-reference': '2412223539978676096',
                    phone: '',
                    'passenger-number': '6',
                    'car-type': ''
                },
                originalData: {
                    originalText: `商铺订单320元
订单号2412223539978676096
联系电话：+60123456789
人数：6大一小`
                },
                expectedPhone: '+60123456789',
                expectedCarType: 'mpv'
            },
            {
                name: '测试2: 无电话号码 + 3人乘客',
                orderData: {
                    'ota-reference': '1234567890123456789',
                    phone: '',
                    'passenger-number': '3',
                    'car-type': ''
                },
                originalData: {
                    originalText: `商铺订单250元
订单号1234567890123456789
人数：3人`
                },
                expectedPhone: '1234567890123456789', // Should use order reference
                expectedCarType: 'sedan'
            },
            {
                name: '测试3: 有车型 + 8人乘客',
                orderData: {
                    'ota-reference': '9876543210987654321',
                    phone: '',
                    'passenger-number': '8',
                    'car-type': 'van' // Already specified
                },
                originalData: {
                    originalText: `商铺订单400元
订单号9876543210987654321
人数：8人
车型：van`
                },
                expectedPhone: '9876543210987654321',
                expectedCarType: 'van' // Should keep existing
            }
        ];

        let passCount = 0;
        const totalTests = testCases.length;

        for (const testCase of testCases) {
            console.log(`\n🔍 ${testCase.name}`);

            try {
                // Test phone enhancement
                const phoneEnhanced = window.enhancePhoneNumberExtraction(
                    JSON.parse(JSON.stringify(testCase.orderData)),
                    testCase.originalData
                );

                // Test car type enhancement
                const carTypeEnhanced = window.enhanceCarTypeRecognition(phoneEnhanced);

                // Verify results
                const phoneCorrect = carTypeEnhanced.phone === testCase.expectedPhone;
                const carTypeCorrect = carTypeEnhanced['car-type'] === testCase.expectedCarType;

                if (phoneCorrect && carTypeCorrect) {
                    passCount++;
                    console.log(`✅ 通过: 电话=${carTypeEnhanced.phone}, 车型=${carTypeEnhanced['car-type']}`);
                } else {
                    console.log(`❌ 失败:`);
                    console.log(`   电话 - 期望: ${testCase.expectedPhone}, 实际: ${carTypeEnhanced.phone} ${phoneCorrect ? '✅' : '❌'}`);
                    console.log(`   车型 - 期望: ${testCase.expectedCarType}, 实际: ${carTypeEnhanced['car-type']} ${carTypeCorrect ? '✅' : '❌'}`);
                }

            } catch (error) {
                console.error(`❌ 测试错误: ${testCase.name} - ${error.message}`);
            }
        }

        const passRate = (passCount / totalTests * 100).toFixed(1);
        console.log(`\n📊 测试结果: ${passCount}/${totalTests} (${passRate}%)`);

        if (passCount === totalTests) {
            console.log('🎉 所有订单增强功能测试通过！');
            console.log('✅ 电话号码提取与同步功能正常');
            console.log('✅ 智能车型识别功能正常');
        } else {
            console.warn('⚠️ 部分测试失败，请检查增强功能');
        }

        return { passCount, totalTests, passRate };
    }

    // 将增强功能测试函数暴露到全局作用域
    window.testOrderEnhancements = testOrderEnhancements;
    window.enhancePhoneNumberExtraction = enhancePhoneNumberExtraction;
    window.enhanceCarTypeRecognition = enhanceCarTypeRecognition;

    </script>
    <!-- API加载器 -->
    <div id="api-loader">
        <div class="spinner-container">
            <div class="spinner"></div>
            <div class="spinner-text">正在处理，请稍候...</div>
        </div>
    </div>
</body>
</html>